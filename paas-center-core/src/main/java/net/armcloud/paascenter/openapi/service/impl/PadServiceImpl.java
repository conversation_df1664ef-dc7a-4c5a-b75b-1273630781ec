package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.constants.LockKeyConstants;
import net.armcloud.paascenter.cms.mapper.ConfigurationMapper;
import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailImageSuccMapper;
import net.armcloud.paascenter.cms.model.request.*;
import net.armcloud.paascenter.common.client.component.CommonPadCommandComponent;
import net.armcloud.paascenter.common.client.component.CommonPadTaskComponent;
import net.armcloud.paascenter.common.client.internal.dto.*;
import net.armcloud.paascenter.common.client.internal.dto.command.*;
import net.armcloud.paascenter.common.client.internal.feign.ContainerDeviceFeignClient;
import net.armcloud.paascenter.common.client.internal.feign.ContainerPadFeignClient;
import net.armcloud.paascenter.common.client.internal.vo.*;
import net.armcloud.paascenter.common.client.internal.vo.DeviceInfoVo;
import net.armcloud.paascenter.common.client.internal.vo.GeneratePadTaskVO;
import net.armcloud.paascenter.common.core.constant.Constants;
import net.armcloud.paascenter.common.core.constant.NumberConsts;
import net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum;
import net.armcloud.paascenter.common.core.constant.paas.ImageUploadStatus;
import net.armcloud.paascenter.common.core.constant.pad.PadConstants;
import net.armcloud.paascenter.common.core.constant.pad.PadStatusConstant;
import net.armcloud.paascenter.common.core.constant.task.RedisTaskQueueConstants;
import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode;
import net.armcloud.paascenter.common.enums.EdgeClusterConfigurationEnum;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.bo.task.PadTaskBO;
import net.armcloud.paascenter.common.model.bo.task.quque.*;
import net.armcloud.paascenter.common.model.dto.api.*;
import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetailImageSucc;
import net.armcloud.paascenter.common.model.entity.paas.*;
import net.armcloud.paascenter.common.model.entity.rtc.PadMacLog;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.model.vo.api.*;
import net.armcloud.paascenter.common.model.vo.job.SpeedLimitVO;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyTime;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.common.rocketmq.concurrent.CommonThreadPoolExecutors;
import net.armcloud.paascenter.common.utils.DingTalkRobotClient;
import net.armcloud.paascenter.common.utils.FileUtils;
import net.armcloud.paascenter.common.utils.MACUtils;
import net.armcloud.paascenter.common.utils.http.HttpUrlUtils;
import net.armcloud.paascenter.openapi.exception.code.PadExceptionCode;
import net.armcloud.paascenter.openapi.helper.NetStoragePadHelper;
import net.armcloud.paascenter.openapi.manager.*;
import net.armcloud.paascenter.openapi.mapper.*;
import net.armcloud.paascenter.openapi.model.dto.*;
import net.armcloud.paascenter.openapi.model.dto.netstorage.*;
import net.armcloud.paascenter.openapi.model.vo.*;
import net.armcloud.paascenter.openapi.model.vo.netstorage.NetStorageCreateVO;
import net.armcloud.paascenter.openapi.service.*;
import net.armcloud.paascenter.openapi.service.netstorage.NetStorageComputeUnitService;
import net.armcloud.paascenter.openapi.service.netstorage.NetStorageResUnitService;
import net.armcloud.paascenter.openapi.utils.*;
import net.armcloud.paascenter.task.controller.internal.TaskInternalController;
import net.armcloud.paascenter.task.mapper.PadTaskMapper;
import net.armcloud.paascenter.task.model.vo.PadEdgeVO;
import net.armcloud.paascenter.task.service.IPadTaskService;
import net.armcloud.paascenter.task.service.ITaskRetryService;
import net.armcloud.paascenter.task.service.ITaskService;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustAllStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static java.lang.Boolean.FALSE;
import static net.armcloud.paascenter.cms.constants.LockKeyConstants.IpClashLock.CBS_INSTANCE_IP;
import static net.armcloud.paascenter.common.core.constant.Constants.*;
import static net.armcloud.paascenter.common.core.constant.NumberConsts.*;
import static net.armcloud.paascenter.common.core.constant.SystemConfigurationConstants.INSTANCE_DEFAULT_DNS;
import static net.armcloud.paascenter.common.core.constant.comms.CommsConstant.DataField.SUB_TASK_ID;
import static net.armcloud.paascenter.common.core.constant.comms.CommsConstant.DataField.TASK_ID;
import static net.armcloud.paascenter.common.core.constant.paas.DictConstants.Type.PAD_PREVIEW_EXPIRATION_SECOND;
import static net.armcloud.paascenter.common.core.constant.paas.PadConstant.CloudVendorType.ARM_CLOUD_VENDOR_TYPE;
import static net.armcloud.paascenter.common.core.constant.paas.PadConstant.OnlineValue.OFFLINE;
import static net.armcloud.paascenter.common.core.constant.paas.PadConstant.OnlineValue.ONLINE;
import static net.armcloud.paascenter.common.core.constant.paas.PadOperBusinessType.ADD;
import static net.armcloud.paascenter.common.core.constant.paas.PadOperBusinessType.MODIFY;
import static net.armcloud.paascenter.common.core.constant.pad.PadStatusConstant.NOT_READY;
import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.EXECUTING;
import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.WAIT_EXECUTE;
import static net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants.*;
import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.*;
import static net.armcloud.paascenter.common.enums.SourceTargetEnum.PAAS;
import static net.armcloud.paascenter.commscenter.constant.CacheKeyPrefixConstant.PAD_REPORTED_DING_TALK;
import static net.armcloud.paascenter.openapi.constants.CacheKeyConstants.PAD_INSTALLED_APP_PREFIX;
import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.*;
import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.PAD_CODE_NOT_EXIST;
import static org.springframework.util.ObjectUtils.isEmpty;

@Slf4j
@Service
public class PadServiceImpl implements IPadService {
    private final PadMapper padMapper;
    private final PadManager padManager;

    @Resource
    private ArmServerMapper armServerMapper;
    private final PadGroupMapper padGroupMapper;
    private final DictManager dictManager;

    @Resource
    private NetStorageResMapper netStorageResMapper;

    private NetStorageResPadMapper netStorageResPadMapper;


    @Resource
    private NetStorageResService netStorageResService;
    private final DcInfoMapper dcInfoMapper;
    private final FileManager fileManager;
    private final DcInfoManager dcInfoManager;
    private final PadOutMapper padOutMapper;
    private final IPadStatusService padStatusService;
    private final PadCommandManager padCommandManager;
    private final CommonPadTaskComponent padTaskComponent;
    private final PadPropertiesMapper padPropertiesMapper;
    private final IPadPropertiesKeyService padPropertiesKeyService;
    private final CustomerUploadImageMapper customerUploadImageMapper;
    private final PadStatusMapper padStatusMapper;

    private final NetStoragePadUnitDetailMapper netStoragePadUnitDetailMapper;


    private final PadRoomMapper padRoomMapper;
    private final DevicePadMapper devicePadMapper;
    private final INetStorageResOffLogService netStorageResOffLogService;
    private final PadOperLogMapper padOperLogMapper;
    private final AppBlackMapper appBlackMapper;
    private final RedisService redisService;
    private final CommonPadCommandComponent padCommandComponent;
    private final PadMacLogMapper padMacLogMapper;
    private final PadInstalledAppInformationMapper padInstalledAppInformationMapper;
    private final RealPhoneTemplateMapper realPhoneTemplateMapper;

    private final AdiCertificateManager adiCertificateManager;

    private final AndroidDeviceInfoService androidDeviceInfoService;
    private final IArmService armServerService;
    private final ITaskService taskService;
    private final IPadTaskService padTaskService;

    private final TaskRelInstanceDetailImageSuccMapper taskRelInstanceDetailImageSuccMapper;

    @Resource
    private CustomerDeviceMapper customerDeviceMapper;
    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private ContainerDeviceFeignClient containerDeviceFeignClient;


    @Resource
    private CustomerConfigMapper customerConfigMapper;


    @Resource
    private ResourceSpecificationMapper resourceSpecificationMapper;
    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private ContainerPadFeignClient containerPadFeignClient;

    @Resource
    private NetStoragePadHelper netStoragePadHelper;

    @Resource
    private NetStorageComputeUnitService netStorageComputeUnitService;

    @Resource
    private EdgeClusterMapper edgeClusterMapper;

    @Resource
    private AdiTemplateCustomerMapper adiTemplateCustomerMapper;

    @Resource
    private ITaskRetryService taskRetryService;


    public static final String NET_WOER_STOTAGE_DING = "https://oapi.dingtalk.com/robot/send?access_token=f03a7c93f45786183a29defb9639b5e376825f30272093363b9ff8e561a796b5";



    private static final String USER_ROLES_PREFIX = "USER:ROLES:";
    private static int corePoolSize = Runtime.getRuntime().availableProcessors() * 4;
    private static final ExecutorService EXECUTOR = CommonThreadPoolExecutors
            .newDiscardThreadPoolExecutor(corePoolSize + 1, corePoolSize * 8, "generatePreview_threadPool");
    private static final ExecutorService SWITCH_PUBLIC_IP_EXECUTOR = CommonThreadPoolExecutors
            .newDiscardThreadPoolExecutor(corePoolSize + 1, corePoolSize * 8, "switch_public_threadPool");

    private final ScreenLayoutMapper screenLayoutMapper;
    private final CustomerAppClassifyPadRelationMapper customerAppClassifyPadRelationMapper;
    private final AppWhiteMapper appWhiteMapper;

    private final CustomerAppClassifyRelationMapper customerAppClassifyRelationMapper;
    private final CustomerAppClassifyMapper customerAppClassifyMapper;

    private final RedissonDistributedLock redissonDistributedLock;

    private final ConfigurationMapper configurationMapper;


    private final TaskInternalController taskInternalController;

    private final IEdgeClusterConfigurationService edgeClusterConfigurationService;


    @Value("${spring.profiles.active:unknown}")
    private String springProfilesActive;

    /**
     * 是否走创建实例新规则
     */
    @Value("${createPadCodeNew:false}")
    private Boolean createPadCodeNew;

    /**
     * 是否走创建实例新规则
     */
    @Value("${net.storage.dns:8.8.8.8}")
    private String dns;

    @Value("${android-prop.armcloud-server-addr}")
    public String armcloudServerAddr;

    public final static String screenshotUrl ="/api/agent/pad/acquireScreenshotPermission";

    private final Cache<String, String> PAD_CODE_SEND_CACHE = CacheBuilder.newBuilder()
            .concurrencyLevel(5)
            .initialCapacity(100)
            .maximumSize(1000)  // 最大缓存条目数
            .expireAfterWrite(5, TimeUnit.MINUTES)  // 设置缓存五分钟过期
            .build();

    public String getOrWriteToCache(String key) {
        // 检查缓存中是否已经存在该 key
        String cachedValue = PAD_CODE_SEND_CACHE.getIfPresent(key);

        // 如果缓存中存在该 key，返回缓存的 value
        if (cachedValue != null) {
            return cachedValue;
        }

        // 如果缓存中不存在该 key，则将 key 和 value 写入缓存
        PAD_CODE_SEND_CACHE.put(key, key);  // key 和 value 都是 key
        return "";  // 返回空字符串表示已写入缓存
    }


    @Resource
    private PadTaskMapper padTaskMapper;

    @Autowired
    private NetPadMapper netPadMapper;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private NetStorageComputeUnitMapper netStorageComputeUnitMapper;

    @Resource
    NetStorageComputeUnitPadMapper netStorageComputeUnitPadMapper;

    @Override
    public Boolean checkPadListOwnerService(Long customerId, List<String> padCodes) {
        // 构建查询条件
        QueryWrapper<Pad> queryWrapper = new QueryWrapper<>();
        if (redisService.isAdmin(customerId)) {
            // 如果是管理员，只查询 pad_code 条件
            queryWrapper.in("pad_code", padCodes);
        } else {
            // 如果不是管理员，查询 customer_id 和 pad_code 条件
            queryWrapper.eq("customer_id", customerId)
                    .in("pad_code", padCodes);
        }
        Long count = padMapper.selectCount(queryWrapper);
        if (count != padCodes.stream().distinct().count()) {
            throw new BasicException(PAD_CODE_NOT_EXIST);
        }
        return true;
    }

    @Override
    public Boolean checkPadListAndDeviceOnlineOwnerService(Long customerId, List<String> padCodes,boolean skipVerifyingTheOff) {
        // 构建查询条件
        if (CollUtil.isEmpty(padCodes)) {
            throw new BasicException(PAD_CODE_NOT_EMPTY);
        }
        QueryWrapper<Pad> queryWrapper = new QueryWrapper<>();
        if (redisService.isAdmin(customerId)) {
            // 如果是管理员，只查询 pad_code 条件
            queryWrapper.in("pad_code", padCodes);
        } else {
            // 如果不是管理员，查询 customer_id 和 pad_code 条件
            queryWrapper.eq("customer_id", customerId)
                    .in("pad_code", padCodes);
        }
        Long count = padMapper.selectCount(queryWrapper);
        if (count != padCodes.stream().distinct().count()) {
            throw new BasicException(PAD_CODE_NOT_EXIST);
        }
        //如果需要验证实例处于关机状态,去数据库获取实例状态
        if(skipVerifyingTheOff){
            padStatusMapper.listByPadCodes(padCodes).stream().filter(padStatus -> Objects.equals(padStatus.getPadStatus(),PadStatusConstant.OFF)).findFirst().ifPresent(padStatus -> {
                throw new BasicException(PadExceptionCode.NETWORK_INSTANCE_STATUS_OFF);
            });
        }
        List<DevicePadVO> devicePadVOS = devicePadMapper.listByPadCode(padCodes);
        List<DevicePadVO> filteredList = devicePadVOS.stream()
                .filter(device -> Objects.equals(device.getDeviceStatus(), 0))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(filteredList)) {
            String padCodeListStr = filteredList.stream()
                    .map(DevicePadVO::getPadCode) // 提取 padCode
                    .collect(Collectors.joining(","));
            String format = String.format(DEVICE_NOT_ONLINE.getMsg(), padCodeListStr);
            // 板卡离线并且有用户调用,发送钉钉消息
//            DingTalkPadClient.sendMessage(format, springProfilesActive);
            throw new BasicException(format, DEVICE_NOT_ONLINE.getStatus());
        }
        return true;
    }

    @Override
    public List<GeneratePadTaskVO> screenshotLocal(ScreenshotLocalDTO param) {
        long customerId = param.getCustomerId();
        PadCMDForwardDTO padCMDForwardDTO = new ScreenshotLocalCMDDTO()
                .setBroadcast(param.getBroadcast())
                .setRotation(param.getRotation())
                .setResolutionHeight(param.getResolutionHeight())
                .setResolutionWidth(param.getResolutionWidth())
                .setDefinition(param.getDefinition())
                .builderForwardDTO(param.getPadCodes(), PAAS);

        PadTaskBO padTaskBO = padTaskComponent.addPadCMDTask(customerId, param.getPadCodes(), SCREENSHOT_LOCAL,
                padCMDForwardDTO);
        return GeneratePadTaskVO.builder(padTaskBO);
    }

    @Override
    public List<LongPreviewVO> getLongGenerateUrl(LongPreviewDTO param) {
        // 校验 pad 是否属于当前客户且状态为正常
        validatePadOwnership(param);

        Map<String, Integer> padStatusMap = getPadStatusMap(param.getPadCodes());

        // 查询 Pad 对应的集群信息
        List<PadEdgeVO> padEdgeVOS = padTaskService.queryPadClusterInfo(param.getPadCodes());
        if (CollUtil.isEmpty(padEdgeVOS)) {
            log.warn("getLongGenerateUrl - 查询不到pad信息，param: {}", param);
            return Collections.emptyList();
        }

        // 按 clusterCode 分组，后续同集群一起请求
        Map<String, List<PadEdgeVO>> clusterPadMap = padEdgeVOS.stream()
                .filter(p -> p.getClusterCode() != null)
                .collect(Collectors.groupingBy(PadEdgeVO::getClusterCode));

        List<LongPreviewVO> result = new ArrayList<>();

        // 逐个集群处理
        for (Map.Entry<String, List<PadEdgeVO>> entry : clusterPadMap.entrySet()) {
            String clusterCode = entry.getKey();
            List<PadEdgeVO> pads = entry.getValue();

            // 调用边缘接口或返回 pad 状态异常
            result.addAll(processPadsForCluster(clusterCode, pads, padStatusMap, param));
        }

        log.info("getLongGenerateUrl - 返回结果: {}", JSONObject.toJSONString(result));
        return result;
    }

    private void validatePadOwnership(LongPreviewDTO param) {
        LambdaQueryWrapper<Pad> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Pad::getPadCode, param.getPadCodes());
        queryWrapper.eq(Pad::getCustomerId, param.getCustomerId());
        queryWrapper.eq(Pad::getStatus, ONE);

        Long count = padMapper.selectCount(queryWrapper);
        if (count != param.getPadCodes().stream().distinct().count()) {
            throw new BasicException(PAD_CODE_NOT_EXIST);
        }
    }

    private Map<String, Integer> getPadStatusMap(List<String> padCodes) {
        LambdaQueryWrapper<PadStatus> lam = new LambdaQueryWrapper<>();
        lam.in(PadStatus::getPadCode, padCodes);
        lam.select(PadStatus::getPadStatus, PadStatus::getPadCode);

        return padStatusService.list(lam).stream()
                .collect(Collectors.toMap(PadStatus::getPadCode, PadStatus::getPadStatus));
    }


    private List<LongPreviewVO> processPadsForCluster(String clusterCode, List<PadEdgeVO> pads,
                                                      Map<String, Integer> padStatusMap, LongPreviewDTO longPreviewDTO) {
        List<LongPreviewVO> result = new ArrayList<>();

        List<PadEdgeVO> nonRunning = pads.stream()
                .filter(p -> !PadStatusConstant.RUNNING.equals(padStatusMap.get(p.getPadCode())))
                .collect(Collectors.toList());

        for (PadEdgeVO pad : nonRunning) {
            result.add(buildFailureVO(pad.getPadCode(), "实例状态异常"));
        }

        List<PadEdgeVO> running = pads.stream()
                .filter(p -> PadStatusConstant.RUNNING.equals(padStatusMap.get(p.getPadCode())))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(running)) {
            return result;
        }

        try {
            String edgeApiBaseUrl = edgeClusterConfigurationService.getEdgeClusterConfigurationByKey(clusterCode, EdgeClusterConfigurationEnum.EDGE_API_BASE_URL);
            String edgeApiAuthToken = edgeClusterConfigurationService.getEdgeClusterConfigurationByKey(clusterCode, EdgeClusterConfigurationEnum.EDGE_API_AUTH_TOKEN);

            if (StrUtil.isBlank(edgeApiBaseUrl) || StrUtil.isBlank(edgeApiAuthToken)) {
                log.warn("getLongGenerateUrl - clusterCode:{} 配置缺失，edgeApiBaseUrl:{}，edgeApiAuthToken:{}",
                        clusterCode, edgeApiBaseUrl, edgeApiAuthToken);
                for (PadEdgeVO pad : running) {
                    result.add(buildFailureVO(pad.getPadCode(), "边缘配置缺失"));
                }
                return result;
            }

            String url = edgeApiBaseUrl + screenshotUrl;
            JSONObject requestBody = buildEdgeRequestBody(edgeApiAuthToken, longPreviewDTO, running);

            log.info("getLongGenerateUrl - 请求URL: {}，请求体: {}", url, requestBody.toJSONString());

            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(requestBody.toJSONString())
                    .timeout(3000)
                    .execute();

            if (response.getStatus() != 200) {
                log.error("getLongGenerateUrl - 请求失败，状态码: {}，响应: {}", response.getStatus(), response.body());
                for (PadEdgeVO pad : running) {
                    result.add(buildFailureVO(pad.getPadCode(), "边缘请求失败"));
                }
                return result;
            }

            JSONObject resp = JSONObject.parseObject(response.body());
            if (!Integer.valueOf(0).equals(resp.getInteger("code"))) {
                log.error("getLongGenerateUrl - 响应返回失败，body: {}", resp.toJSONString());
                for (PadEdgeVO pad : running) {
                    result.add(buildFailureVO(pad.getPadCode(), "边缘请求失败"));
                }
                return result;
            }

            JSONArray dataArray = resp.getJSONArray("data");
            if (dataArray != null) {
                result.addAll(dataArray.stream().map(item -> {
                    JSONObject obj = (JSONObject) item;
                    LongPreviewVO vo = new LongPreviewVO();
                    vo.setPadCode(obj.getString("padCode"));
                    vo.setUrl(obj.getString("url"));
                    vo.setExpireAt(obj.getLong("expireAt"));
                    vo.setSuccess(true);
                    return vo;
                }).collect(Collectors.toList()));
            }

        } catch (Exception e) {
            log.error("getLongGenerateUrl - 调用 clusterCode:{} 时异常", clusterCode, e);
            for (PadEdgeVO pad : running) {
                result.add(buildFailureVO(pad.getPadCode(), "边缘请求异常"));
            }
        }

        return result;
    }

    private LongPreviewVO buildFailureVO(String padCode, String reason) {
        LongPreviewVO vo = new LongPreviewVO();
        vo.setPadCode(padCode);
        vo.setSuccess(false);
        vo.setReason(reason);
        return vo;
    }

    private JSONObject buildEdgeRequestBody(String token, LongPreviewDTO longPreviewDTO, List<PadEdgeVO> pads) {
        JSONObject body = new JSONObject();
        body.put("key", token);
        body.put("format", longPreviewDTO.getFormat().name());

        if (StrUtil.isNotBlank(longPreviewDTO.getHeight())) {
            body.put("height", longPreviewDTO.getHeight());
        }
        if (StrUtil.isNotBlank(longPreviewDTO.getQuality())) {
            body.put("quality", longPreviewDTO.getQuality());
        }
        if (StrUtil.isNotBlank(longPreviewDTO.getWidth())) {
            body.put("width", longPreviewDTO.getWidth());
        }


        JSONArray padsArray = new JSONArray();
        for (PadEdgeVO pad : pads) {
            JSONObject padJson = new JSONObject();
            padJson.put("cbsIp", pad.getDeviceIp());
            padJson.put("padCode", pad.getPadCode());
            padsArray.add(padJson);
        }
        body.put("pads", padsArray);
        return body;
    }



    @Override
    public List<GeneratePreviewVO> generatePreview(ScreenshotLocalDTO param) {
        List<String> removePadCodes = new ArrayList<>();
        List<String> padCodes = param.getPadCodes();
        padCodes.parallelStream().forEach(padCode -> {
            boolean limit = padManager.getGeneratePreviewMark(padCode);
            if (limit) {
                removePadCodes.add(padCode);
            }
        });

        // 移除操作频繁受限的示例
        padCodes.removeAll(removePadCodes);
        if (CollectionUtils.isEmpty(padCodes)) {
            throw new BasicException(OPERATION_TOO_FREQUENT);
        }

        // 添加频率限制
        padManager.generatePreviewMark(padCodes);

        long padPreviewExpirationSecond = Long
                .parseLong(dictManager.getSingleValueByType(PAD_PREVIEW_EXPIRATION_SECOND));
        PadCMDForwardDTO padCMDForwardDTO = new UploadPreviewCMDDTO()
                .setBroadcast(param.getBroadcast())
                .setRotation(param.getRotation())
                .setDefinition(param.getDefinition())
                .setResolutionHeight(param.getResolutionHeight())
                .setResolutionWidth(param.getResolutionWidth())
                .builderForwardDTO(param.getPadCodes(), PAAS, padPreviewExpirationSecond);
        padCommandManager.sendPadCommand(padCMDForwardDTO, GENERATE_PREVIEW_FAIL_EXCEPTION);

        List<GeneratePreviewVO> generatePreviewVOS = new ArrayList<>(padCodes.size());
        // 并发数量为10
        int targetGroups = 10;
        // 计算每组大小，最小值为 1
        int groupSize = (int) Math.ceil((double) padCMDForwardDTO.getPadInfos().size()
                / Math.min(targetGroups, padCMDForwardDTO.getPadInfos().size()));
        CountDownLatch latch = new CountDownLatch(padCMDForwardDTO.getPadInfos().size());

        long startTime = System.currentTimeMillis();

        // 使用 Guava 分组
        List<List<PadCMDForwardDTO.PadInfoDTO>> partitionedLists = Lists.partition(padCMDForwardDTO.getPadInfos(),
                groupSize);
        partitionedLists.stream().parallel().forEach(list -> list.forEach(padInfoDTO -> {
            String padCode = padInfoDTO.getPadCode();
            GeneratePreviewVO generatePreviewVO = new GeneratePreviewVO();
            generatePreviewVO.setPadCode(padCode);
            // 拼接文件所在机房域名
            String key = RedisKeyPrefix.OLD_GENERATE_PREVIEW + padCode;
            String oldPath = redisService.getCacheObject(key);
            UploadPreviewCMDDTO uploadPreviewCMDDTO = null;
            if (padInfoDTO.getData() instanceof com.alibaba.fastjson2.JSONObject) {
                uploadPreviewCMDDTO = ((com.alibaba.fastjson2.JSONObject) padInfoDTO.getData())
                        .toJavaObject(UploadPreviewCMDDTO.class);
            } else {
                uploadPreviewCMDDTO = ((UploadPreviewCMDDTO) padInfoDTO.getData());
            }
            String newPath = uploadPreviewCMDDTO.getPath();
            redisService.setCacheObject(key, newPath, RedisKeyTime.minute_5, TimeUnit.MINUTES);


            String edgeClusterCode = edgeClusterMapper.selectEdgeClusterCodeByPadCodeSingle(padCode);
            String ossScreenshotEndpoint = edgeClusterConfigurationService.getEdgeClusterConfigurationByKey(edgeClusterCode, EdgeClusterConfigurationEnum.OSS_SCREENSHOT_ENDPOINT);


            String accessUrl = ossScreenshotEndpoint + newPath;
            generatePreviewVO.setAccessUrl(accessUrl);
            generatePreviewVOS.add(generatePreviewVO);
            try {
                CompletableFuture.runAsync(() -> {
                    try {
                        if (ObjectUtil.isNotNull(oldPath)) {
                            MinIOFileRenameVO minIOFileRenameVO = new MinIOFileRenameVO();
                            minIOFileRenameVO.setOldPath(oldPath);
                            minIOFileRenameVO.setNewPath(newPath);
                            minIOFileRenameVO.setIsDelete(Boolean.FALSE);
                            minIOFileRenameVO.setOverrideExistNewPath(false);

                            String requestParam = JSON.toJSONString(minIOFileRenameVO);
                            doPost(ossScreenshotEndpoint + "/oss/open/object/file/fileRename", requestParam);
                        }
                    } catch (Exception e) {
                        log.error("padCode={} 重命名预览图文件失败：", padCode, e);
                    } finally {
                        latch.countDown();
                    }
                }, EXECUTOR);
            } catch (Exception e) {
                log.error("PadServiceImpl.generatePreview executor run result_error! msg:{}", e.getMessage(), e);
                latch.countDown();
            }
        }));
        // 预览图文件会频繁生成,生成失败也没关系,不用过长时间的等待结果,防止线程池被用完
        try {
            // 等待所有任务完成
            boolean completed = latch.await(2, TimeUnit.SECONDS);
            if (!completed) {
                log.warn("PadServiceImpl.generatePreview The asynchronous task does not complete within 4 seconds，");
            }
        } catch (InterruptedException e) {
            log.error("generatePreview等待异步任务完成时被中断", e);
        }

        // 记录请求结束时间
        long endTime = System.currentTimeMillis();
        if (endTime - startTime > 5000) {
            log.info("PadServiceImpl.generatePreview 接口总耗时. size():{}, 响应时间: {} ms", padCodes.size(),
                    (endTime - startTime));
        }
        return generatePreviewVOS;
    }

    public static CloseableHttpClient createHttpClientWithNoSsl() throws Exception {
        SSLContextBuilder builder = SSLContextBuilder.create();
        builder.loadTrustMaterial(new TrustAllStrategy());
        SSLConnectionSocketFactory ssl_sf = new SSLConnectionSocketFactory(builder.build(),
                NoopHostnameVerifier.INSTANCE);
        return HttpClients.custom().setSSLSocketFactory(ssl_sf).build();
    }

    public static String doPost(String url, String params) throws Exception {
        long startTime = System.currentTimeMillis(); // 记录请求开始时间

        try (CloseableHttpClient httpClient = createHttpClientWithNoSsl()) {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Accept", "application/json");
            httpPost.setHeader("Content-Type", "application/json");
            String charSet = "UTF-8";
            StringEntity entity = new StringEntity(params, charSet);
            httpPost.setEntity(entity);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                long endTime = System.currentTimeMillis(); // 记录请求结束时间
                if ((endTime - startTime) > 2000) {
                    log.info("HTTP POST /oss/open/object/file/fileRename 请求时长超过2秒. URL: {}, 响应时间: {} ms", url,
                            (endTime - startTime));
                }

                return EntityUtils.toString(response.getEntity());
            }
        } catch (Exception e) {
            long errorTime = System.currentTimeMillis(); // 捕获异常时记录时间
            log.error("HTTP POST 请求异常. URL: {}, 响应时间: {} ms, Body: {}", url, (errorTime - startTime), params, e);
            throw e;
        }
    }

    @Override
    public List<AsyncCmdVO> asyncCmd(ExecuteADBDTO param) {
        List<PadInfoVO> padInfoVOList = padMapper.selectPadInfoByCodeList(param.getPadCodes());
        Map<String, PadInfoVO> padInfoVOMap = padInfoVOList.stream()
                .collect(Collectors.toMap(PadInfoVO::getPadCode, padInfoVO -> padInfoVO));
        // 入口已验证padCode存在,这里不需要判空
        // 有实例状态不在线
        param.getPadCodes().removeIf(padCode -> {
            PadInfoVO padInfoVO = padInfoVOMap.getOrDefault(padCode, new PadInfoVO());
            // 长连接不在线,剔除任务列表
            if (!Objects.equals(padInfoVO.getOnline(), 1)) {
                log.warn("PadServiceImpl.asyncCmd padCode:{} online not,remove!", padCode);
                return true;
            }
            return false;
        });
        // 所有实例都不在线
        if (CollectionUtils.isEmpty(param.getPadCodes())) {
            throw new BasicException(BasicExceptionCode.PAD_CODE_ONLINE_NOT_SUCCESS);
        }
        long customerId = param.getCustomerId();
        PadCMDForwardDTO padCMDForwardDTO = new ExecuteADBCMDDTO()
                .setCmd(param.getScriptContent())
                .builderForwardDTO(param.getPadCodes(), PAAS);
        PadTaskBO padTaskBO = padTaskComponent.addPadCMDTask(customerId, param.getPadCodes(), EXECUTE_COMMAND,
                padCMDForwardDTO);

        List<PadTaskBO.PadSubTaskBO> subTasks = padTaskBO.getSubTasks();
        List<AsyncCmdVO> asyncCmdVOS = new ArrayList<>();
        subTasks.forEach(subTask -> {
            String padCode = subTask.getPadCode();
            Integer customerTaskId = subTask.getCustomerTaskId();
            AsyncCmdVO asyncCmdVO = new AsyncCmdVO();
            asyncCmdVO.setPadCode(padCode);
            asyncCmdVO.setTaskId(customerTaskId);
            asyncCmdVO.setVmStatus(Boolean.TRUE.equals(subTask.getSendCmdSuccess()) ? ONLINE : OFFLINE);
            asyncCmdVOS.add(asyncCmdVO);
        });
        return asyncCmdVOS;
    }

    @Override
    public List<GeneratePadTaskVO> uploadFile(PadDownloadFileDTO param) {
        long customerId = param.getCustomerId();
        SourceTargetEnum sourceTarget = Optional.of(param).map(PadDownloadFileDTO::getTaskSource)
                .orElse(SourceTargetEnum.PAAS);
        String oprBy = Optional.of(param).map(PadDownloadFileDTO::getOprBy).orElse(String.valueOf(customerId));
        // 查询可能重复的任务（无fileId场景，返回content_json用于中过滤）
        log.info("PadServiceImpl.getConflictTask.padCodes: {}", param);
//        List<Map<String, Object>> potentialConflictTasks = padTaskMapper.findConflictPadCodesByTaskQueue(
//                customerId, param.getPadCodes());
//        log.info("PadServiceImpl.uploadFile.potentialConflictTasks: {}", potentialConflictTasks);

//        Set<String> conflictPadCodes = getConflictTask(param.getPadCodes(), param.getFileDownloadUrl(),potentialConflictTasks);
        // 转换为Set，便于快速查找
//        Set<String> conflictPadCodesSet = new HashSet<>(conflictPadCodes);

        // 创建返回结果列表
        List<GeneratePadTaskVO> resultList = new ArrayList<>();

        // 处理冲突的padCode
//        for (String padCode : conflictPadCodes) {
//            GeneratePadTaskVO vo = new GeneratePadTaskVO();
//            vo.setPadCode(padCode);
//            vo.setTaskStatus(-1); // 表示任务已存在，请勿重复提交
//            resultList.add(vo);
//        }

        // 未冲突的padCode列表，用于创建新任务
//        List<String> nonConflictPadCodes = param.getPadCodes().stream()
//                .filter(padCode -> !conflictPadCodesSet.contains(padCode))
//                .collect(Collectors.toList());
        List<String> nonConflictPadCodes = param.getPadCodes();

        // 只有在有非冲突的padCode时才创建新任务
        if (!nonConflictPadCodes.isEmpty()) {
            PadCMDForwardDTO padCMDForwardDTO = new PadDownloadFileCMDDTO().setPath(param.getFileDownloadUrl())
                    .setFileName(param.getFileName()).setInstall(param.getInstall())
                    .setTargetDirectory(param.getTargetDirectory())
                    .builderForwardDTO(nonConflictPadCodes, sourceTarget, oprBy);

            PadTaskBO padTaskBO = padTaskComponent.addPadCMDTask(customerId, nonConflictPadCodes, DOWNLOAD_FILE,
                    padCMDForwardDTO);

            List<GeneratePadTaskVO> newTaskVOs = GeneratePadTaskVO.builder(padTaskBO);

            resultList.addAll(newTaskVOs);
        }

        return resultList;
    }

    @Override
    public List<GeneratePadTaskVO> uploadFileV2(PadDownloadFileV2DTO param) {
        Long customerId = param.getCustomerId();
        if (redisService.isAdmin(customerId)) {
            customerId = null;
        }
        CustomerFileVO customerFileVO = fileManager.getCustomerExistingFile(customerId, param.getFileUniqueId());
        if (Objects.isNull(customerFileVO)) {
            throw new BasicException(FILE_NOT_AVAILABLE_EXCEPTION);
        }
        customerId = param.getCustomerId();
        fileManager.refreshLastUseTime(customerFileVO.getFileId());
        String customerFileName = customerFileVO.getCustomerFileName();
        if (StringUtils.isEmpty(customerFileName)) {
            if (StringUtils.isNotEmpty(customerFileVO.getFileExt())) {
                customerFileName = customerFileVO.getFileMd5() + "." + customerFileVO.getFileExt();
            } else {
                customerFileName = customerFileVO.getFileMd5();
            }
        }
        // 九天客户需求,如果文件名不为.apk后缀,就将需要安装改成不需要安装 xapk apks
        if (StringUtils.isNotEmpty(customerFileName) && param.getAutoInstall() == 1
                && !StringUtils.endsWithAny(StringUtils.lowerCase(customerFileName), ".apk", ".xapk", ".apks")) {
            param.setAutoInstall(0);
        }

//        List<Map<String, Object>> potentialConflictTasks = padTaskMapper.findConflictPadCodesByFileId(
//                customerId, param.getPadCodes(), customerFileVO.getCustomerFileId());
//        log.info("PadServiceImpl.uploadFileV2.conflictPadCodes: {}", potentialConflictTasks);

        // 查询重复任务的padCode列表（有fileId场景，根据customizeFilePath判断）
//        Set<String> conflictPadCodes = getConflictTask(param.getPadCodes(), customerFileVO.getPublicUrl(),potentialConflictTasks);

        // 转换为Set，便于快速查找
//        Set<String> conflictPadCodesSet = new HashSet<>(conflictPadCodes);

        // 创建返回结果列表
        List<GeneratePadTaskVO> resultList = new ArrayList<>();

        // 处理冲突的padCode
//        for (String padCode : conflictPadCodes) {
//            GeneratePadTaskVO vo = new GeneratePadTaskVO();
//            vo.setPadCode(padCode);
//            vo.setTaskStatus(-1); // 表示任务已存在，请勿重复提交
//            resultList.add(vo);
//        }

        // 未冲突的padCode列表，用于创建新任务
//        List<String> nonConflictPadCodes = param.getPadCodes().stream()
//                .filter(padCode -> !conflictPadCodesSet.contains(padCode))
//                .collect(Collectors.toList());
        List<String> nonConflictPadCodes = param.getPadCodes();

        // 只有在有非冲突的padCode时才创建新任务
        if (!nonConflictPadCodes.isEmpty()) {
            String fileName = param.getAutoInstall() == 1 ? UuidUtils.generateUuid() : customerFileName;
            SourceTargetEnum sourceTarget = Optional.of(param).map(PadDownloadFileV2DTO::getTaskSource)
                    .orElse(SourceTargetEnum.PAAS);
            String oprBy = Optional.of(param).map(PadDownloadFileV2DTO::getOprBy).orElse(String.valueOf(customerId));
            PadDownloadFileCMDDTO padDownloadFileCMDDTO = new PadDownloadFileCMDDTO().setFileName(fileName)
                    .setInstall(param.getAutoInstall() == ONE ? true : false)
                    .setIsAuthorization(Objects.isNull(param.getIsAuthorization()) || param.getIsAuthorization())
                    .setTargetDirectory(param.getCustomizeFilePath())
                    .setCustomerFileId(customerFileVO.getCustomerFileId())
                    .setPath(customerFileVO.getPublicUrl());
            // 不需要安装,不传包名 TODO 兼容安卓白名单设置
            if (!Objects.equals(param.getAutoInstall(), 1)) {
                padDownloadFileCMDDTO.setPackageName(StringUtils.EMPTY);
            }
            PadCMDForwardDTO padCMDForwardDTO = padDownloadFileCMDDTO.builderForwardDTO(nonConflictPadCodes, sourceTarget, oprBy);

            Consumer<AddPadTaskDTO> padTaskConsumer = addPadTaskDTO -> {
                addPadTaskDTO.setCustomerFileId(customerFileVO.getCustomerFileId());
            };
            customerId = Optional.of(customerId).orElse(0L);
            PadTaskBO padTaskBO = padTaskComponent.addPadTask(customerId, nonConflictPadCodes, DOWNLOAD_FILE, padTaskConsumer, JSON.toJSONString(padCMDForwardDTO), PAAS);

            List<GeneratePadTaskVO> newTaskVOs = GeneratePadTaskVO.builder(padTaskBO);
            resultList.addAll(newTaskVOs);
        }

        return resultList;
    }

    public List<GeneratePadTaskVO> uploadFileAndInstall(Long customerId, PadDownloadFileV3DTO param) {
        // fileManager.cacheFile(customerId,param.getMd5(),param.getUrl(),param.getFileName());
        String fileName = StringUtils.isEmpty(param.getFileName()) ? UuidUtils.generateUuid() : param.getFileName();
        // 文件名称不包含后缀,并且不需要安装,拼接一个后缀,兼容安卓文件上传逻辑文件必须要有一个后缀
        if (!Objects.equals(param.getAutoInstall(), 1) && !FileUtils.hasSuffix(fileName)) {
            fileName = HttpUrlUtils.getFileSuffix(fileName, param.getUrl());
        }

//        List<Map<String, Object>> potentialConflictTasks = padTaskMapper.findConflictPadCodesByTaskQueue(
//                customerId, param.getPadCodes());
//        log.info("PadServiceImpl.uploadFileAndInstall.potentialConflictTasks: {}", potentialConflictTasks);

        // 查询可能重复的任务
//        Set<String> conflictPadCodes = getConflictTask(param.getPadCodes(), param.getUrl(), potentialConflictTasks);

        // 转换为Set，便于快速查找
//        Set<String> conflictPadCodesSet = new HashSet<>(conflictPadCodes);

        // 创建返回结果列表
        List<GeneratePadTaskVO> resultList = new ArrayList<>();

        // 处理冲突的padCode
//        for (String padCode : conflictPadCodes) {
//            GeneratePadTaskVO vo = new GeneratePadTaskVO();
//            vo.setPadCode(padCode);
//            vo.setTaskStatus(-1); // 表示任务已存在，请勿重复提交
//            resultList.add(vo);
//        }

        // 未冲突的padCode列表，用于创建新任务
//        List<String> nonConflictPadCodes = param.getPadCodes().stream()
//                .filter(padCode -> !conflictPadCodesSet.contains(padCode))
//                .collect(Collectors.toList());
        List<String> nonConflictPadCodes = param.getPadCodes();

        // 只有在有非冲突的padCode时才创建新任务
        if (!nonConflictPadCodes.isEmpty()) {
            SourceTargetEnum sourceTargetEnum = SourceTargetEnum.PAAS;
            String oprBy = Optional.of(param).map(PadDownloadFileV3DTO::getOprBy).orElse(String.valueOf(customerId));
            PadDownloadFileCMDDTO padDownloadFileCMDDTO = new PadDownloadFileCMDDTO().setFileName(fileName)
                    .setInstall(param.getAutoInstall() == ONE ? true : false)
                    .setUrl(param.getUrl())
                    .setMd5(param.getMd5())
                    .setPackageName(param.getPackageName())
                    .setIconUrl(param.getIconPath())
                    .setIsAuthorization(Objects.isNull(param.getIsAuthorization()) || param.getIsAuthorization())
                    .setTargetDirectory(param.getCustomizeFilePath());
            // 不需要安装,不传包名 TODO 兼容安卓白名单设置
            if (!Objects.equals(param.getAutoInstall(), 1)) {
                padDownloadFileCMDDTO.setPackageName(StringUtils.EMPTY);
            }

            PadCMDForwardDTO padCMDForwardDTO = padDownloadFileCMDDTO.builderForwardDTO(nonConflictPadCodes,
                    sourceTargetEnum, oprBy);
            // 根据pad拼接不同文件下载地址
            padCMDForwardDTO.getPadInfos().forEach(padInfoDTO -> {
                String padCode = padInfoDTO.getPadCode();

                String fileDownloadUrl = param.getUrl();
                PadDownloadFileCMDDTO data = (PadDownloadFileCMDDTO) padInfoDTO.getData();
                PadDownloadFileCMDDTO newData = JSON.parseObject(JSON.toJSONString(data), PadDownloadFileCMDDTO.class);
                if (StringUtils.isNotBlank(param.getIconPath())) {
                    String iconDownloadUrl = param.getIconPath();
                    newData.setIconUrl(iconDownloadUrl);
                }
                newData.setPath(fileDownloadUrl);
                if (StringUtils.isNotBlank(param.getUrl())) {
                    newData.setPath(param.getUrl());
                }
                padInfoDTO.setData(newData);
            });
            customerId = Optional.of(customerId).orElse(0L);
            PadTaskBO padTaskBO = padTaskComponent.addPadTask(customerId, nonConflictPadCodes, DOWNLOAD_FILE, null,
                    JSON.toJSONString(padCMDForwardDTO), PAAS);

            List<GeneratePadTaskVO> newTaskVOs = GeneratePadTaskVO.builder(padTaskBO);

            resultList.addAll(newTaskVOs);
        }

        return resultList;
    }


    @Override
    public List<GeneratePadTaskVO> uploadFileV3(PadDownloadFileV3DTO param) {
        log.info("PadServiceImpl.uploadFileV3 param:{}", JSONObject.toJSONString(param));
        Long customerId = param.getCustomerId();
        CustomerFileVO customerFileVO = null;
        // 有传文件ID,通过文件ID查找
        if (Objects.nonNull(param.getFileUniqueId())) {
            customerFileVO = fileManager.getCustomerExistingFile(customerId, param.getFileUniqueId());
        }
        // 没传ID或者找不到,通过MD5 查找.
        if (Objects.isNull(customerFileVO) && !StringUtils.isEmpty(param.getMd5())) {
            customerFileVO = fileManager.getFileIdByCustomerMd5(customerId, param.getMd5());
        }
        // MD5也找不到,但是有传url,直接创建一个文件上传任务
        if (Objects.isNull(customerFileVO) && !StringUtils.isEmpty(param.getUrl())) {
            return uploadFileAndInstall(customerId, param);
        }
        if (Objects.isNull(customerFileVO) || StringUtils.isBlank(customerFileVO.getPublicUrl())) {
            throw new BasicException(FILE_NOT_AVAILABLE_EXCEPTION);
        }

//        List<Map<String, Object>> potentialConflictTasks = padTaskMapper.findConflictPadCodesByFileId(
//                customerId, param.getPadCodes(), customerFileVO.getCustomerFileId());
//        log.info("PadServiceImpl.uploadFileV3.conflictPadCodes: {}", potentialConflictTasks);

        // 查询重复任务的padCode列表（有fileId场景，根据customizeFilePath判断）
//        Set<String> conflictPadCodes = getConflictTask(param.getPadCodes(), customerFileVO.getPublicUrl(),potentialConflictTasks);

        // 转换为Set，便于快速查找
//        Set<String> conflictPadCodesSet = new HashSet<>(conflictPadCodes);
        // 创建返回结果列表
        List<GeneratePadTaskVO> resultList = new ArrayList<>();

        // 处理冲突的padCode
//        for (String padCode : conflictPadCodes) {
//            GeneratePadTaskVO vo = new GeneratePadTaskVO();
//            vo.setPadCode(padCode);
//            vo.setTaskStatus(-1); // 表示任务已存在，请勿重复提交
//            resultList.add(vo);
//        }

        // 未冲突的padCode列表，用于创建新任务
//        List<String> nonConflictPadCodes = param.getPadCodes().stream()
//                .filter(padCode -> !conflictPadCodesSet.contains(padCode))
//                .collect(Collectors.toList());
        List<String> nonConflictPadCodes = param.getPadCodes();

        // 只有在有非冲突的padCode时才创建新任务
        if (!nonConflictPadCodes.isEmpty()) {
            String customerFileName = customerFileVO.getCustomerFileName();
            if (StringUtils.isEmpty(customerFileName)) {
                if (StringUtils.isNotEmpty(customerFileVO.getFileExt())) {
                    customerFileName = customerFileVO.getFileMd5() + "." + customerFileVO.getFileExt();
                } else {
                    customerFileName = customerFileVO.getFileMd5();
                }
            }
            String FileName = param.getAutoInstall() == 1 ? UuidUtils.generateUuid() : customerFileName;
            SourceTargetEnum sourceTarget = Optional.of(param).map(PadDownloadFileV3DTO::getTaskSource)
                    .orElse(SourceTargetEnum.PAAS);
            String oprBy = Optional.of(param).map(PadDownloadFileV3DTO::getOprBy).orElse(String.valueOf(customerId));
            PadDownloadFileCMDDTO padDownloadFileCMDDTO = new PadDownloadFileCMDDTO().setFileName(FileName)
                    .setInstall(param.getAutoInstall() == ONE ? true : false)
                    .setIsAuthorization(Objects.isNull(param.getIsAuthorization()) || param.getIsAuthorization())
                    .setTargetDirectory(param.getCustomizeFilePath())
                    .setCustomerFileId(customerFileVO.getCustomerFileId())
                    .setPath(customerFileVO.getPublicUrl());
            // 不需要安装,不传包名 TODO 兼容安卓白名单设置
            if (!Objects.equals(param.getAutoInstall(), 1)) {
                padDownloadFileCMDDTO.setPackageName(StringUtils.EMPTY);
            }
            PadCMDForwardDTO padCMDForwardDTO = padDownloadFileCMDDTO.builderForwardDTO(nonConflictPadCodes, sourceTarget,
                    oprBy);
            // 根据pad拼接不同文件下载地址
            CustomerFileVO finalCustomerFileVO = customerFileVO;
            Consumer<AddPadTaskDTO> padTaskConsumer = addPadTaskDTO -> {
                addPadTaskDTO.setCustomerFileId(finalCustomerFileVO.getCustomerFileId());
            };
            customerId = Optional.of(customerId).orElse(0L);
            PadTaskBO padTaskBO = padTaskComponent.addPadTask(customerId, nonConflictPadCodes, DOWNLOAD_FILE,
                    padTaskConsumer, JSON.toJSONString(padCMDForwardDTO), PAAS);

            List<GeneratePadTaskVO> newTaskVOs = GeneratePadTaskVO.builder(padTaskBO);

            resultList.addAll(newTaskVOs);
        }

        return resultList;
    }

    @NotNull
    private Set<String> getConflictTask(@NotNull List<String> padCodes, @NotNull String downloadUrl,@NotNull List<Map<String, Object>> potentialConflictTasks) {
        Set<String> conflictPadSet = new HashSet<>();
        Set<String> padCodeSet = new HashSet<>(padCodes); // 转Set提升查询性能
        for (Map<String, Object> taskRow : potentialConflictTasks) {
            String contentJson = (String) taskRow.get("contentJson");
            String padCode = (String) taskRow.get("padCode");
            // 过滤无效数据
            if (StringUtils.isBlank(contentJson) || StringUtils.isBlank(padCode) || !padCodeSet.contains(padCode)) {
                continue;
            }
            try {
                com.alibaba.fastjson2.JSONObject padInfosJson = JSON.parseObject(contentJson);
                com.alibaba.fastjson2.JSONArray jsonArray = padInfosJson.getJSONArray("padInfos");
                if (jsonArray == null || jsonArray.isEmpty()) {
                    continue;
                }
                for (Object item : jsonArray) {
                    com.alibaba.fastjson2.JSONObject contentData = (com.alibaba.fastjson2.JSONObject) item;
                    com.alibaba.fastjson2.JSONObject data = contentData.getJSONObject("data");
                    if (data == null) {
                        continue;
                    }

                    String path = data.getString("path");
                    boolean isUrlMatch = Objects.equals(downloadUrl, path);
                    if (isUrlMatch) {
                        conflictPadSet.add(padCode);
                        break; // 已匹配则跳出内层循环
                    }
                }
            } catch (JSONException e) {
                log.error("JSON解析失败: {}", contentJson, e);
            }
        }
        return conflictPadSet;
    }


    /**
     * 执行同步任务
     * <p>
     */
    @Override
    public List<SyncCmdVO> syncCmd(SyncCmdDTO param, SourceTargetEnum targetEnum) {
        log.debug("syncCmd 入参:{},targetEnum:{}", JSON.toJSONString(param), targetEnum.name());
        long customerId = param.getCustomerId();
        PadInfoVO padInfoVO = padMapper.selectPadInfoByCode(param.getPadCode());
        // 入口已验证padCode存在,这里不需要判空
        // 判断实例长连接在线(先不判断实例状态,后续任务离线失败过多,添加实例状态=10)
        if (!Objects.equals(padInfoVO.getOnline(), 1)) {
            log.warn("PadServiceImpl.syncCmd padCode:{} is not online", param.getPadCode());
            throw new BasicException(BasicExceptionCode.PAD_CODE_ONLINE_NOT_SUCCESS);
        }

        Pad pad = padMapper.selectPadByPadCode(param.getPadCode());
        Boolean isPullMode = pad!=null && pad.getTaskMode() != null && pad.getTaskMode() == 1;

        PadCMDForwardDTO padCMDForwardDTO = new ExecuteADBCMDDTO()
                .setCmd(param.getScriptContent())
                .builderForwardDTO(Collections.singletonList(param.getPadCode()), targetEnum);
        // 20250227 该接口改造为 直接下发到gameserver 并保留任务
        Integer newTaskStatus = isPullMode?WAIT_EXECUTE.getStatus() :EXECUTING.getStatus();
        PadTaskBO padTaskBO = padTaskComponent.addSyncPadCMDTask(customerId, Arrays.asList(param.getPadCode()), EXECUTE_COMMAND, padCMDForwardDTO,newTaskStatus);

        String masterTaskUniqueId = padTaskBO.getMasterTaskUniqueId();
        List<SyncCmdVO> syncCmdVOS = new ArrayList<>();
        Integer customerTaskId = padTaskBO.getSubTasks().get(0).getCustomerTaskId();
        String padCode = padTaskBO.getSubTasks().get(0).getPadCode();

        if(!isPullMode){
            // 下发指令到comms-center
            try {
                PadCMDForwardDTO.PadInfoDTO padInfoDTO = padCMDForwardDTO.getPadInfos().get(0);
                com.alibaba.fastjson2.JSONObject dataJSONObject = com.alibaba.fastjson2.JSONObject
                        .from(padInfoDTO.getData());
                dataJSONObject.put(TASK_ID, padTaskBO.getMasterTaskId());
                dataJSONObject.put(SUB_TASK_ID, padTaskBO.getSubTasks().get(0).getSubTaskId());
                padInfoDTO.setData(dataJSONObject);
                List<CommsTransmissionResultVO> cmdResult = padCommandComponent.sendPadCommand(padCMDForwardDTO);
                List<CommsTransmissionResultVO> failResults = cmdResult.stream()
                        .filter(r -> FALSE.equals(r.getSendSuccess())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(failResults)) {
                    log.error("failResultsIsNotEmpty,padCode:{},cmdResult:{},failResults:{}", padCode,
                            JSON.toJSONString(cmdResult), JSON.toJSONString(failResults));
                    throw new BasicException("无法连接系统服务");
                }
            } catch (Exception e) {
                log.error("execute cmd error>>>padCode:{},padTask:{}", padCode, JSON.toJSONString(padCMDForwardDTO), e);
                throw new BasicException("无法连接系统服务");
            }
        }

        LocalDateTime timeOut = LocalDateTime.now().plusSeconds(5);
        while (LocalDateTime.now().isBefore(timeOut)) {
            if (StrUtil.isNotEmpty(masterTaskUniqueId)) {
                TaskDetailsDTO detailsDTO = new TaskDetailsDTO();
                detailsDTO.setCustomerId(customerId);
                detailsDTO.setMasterTaskUniqueId(masterTaskUniqueId);
                TaskDetailsVO details = taskService.taskDetailsService(detailsDTO);
                String jsonString = JSON.toJSONString(details.getSubTasks().get(0));
                LinkedHashMap<String, Object> map = JSON.parseObject(jsonString, LinkedHashMap.class);
                int taskStatus = (int) map.get("taskStatus");
                SyncCmdVO syncCmdVO = new SyncCmdVO();
                syncCmdVO.setPadCode(padCode);
                syncCmdVO.setTaskId(customerTaskId);
                syncCmdVO.setTaskResult(REQUEST_FAIL);
                if (CollUtil.isNotEmpty(details.getSubTasks())) {
                    Object firstSubTask = details.getSubTasks().get(0);
                    if (firstSubTask instanceof PadTaskViewVO) {
                        PadTaskViewVO subTaskVO = (PadTaskViewVO) firstSubTask;
                        syncCmdVO.setErrorMsg(subTaskVO.getErrorMsg());
                    }
                }
                if (Objects.equals(TaskStatusConstants.SUCCESS.getStatus(), details.getStatus())) {
                    syncCmdVO.setTaskStatus(taskStatus);
                    syncCmdVO.setTaskResult(REQUEST_SUCCESS);
                    syncCmdVOS.add(syncCmdVO);
                    log.debug("syncCmd 出参:{}", JSON.toJSONString(syncCmdVOS));
                    return syncCmdVOS;
                }
            } else {
                TaskDetailsInfoDTO taskDetailsInfoDTO = new TaskDetailsInfoDTO();
                taskDetailsInfoDTO.setCustomerId(customerId);
                taskDetailsInfoDTO.setTaskIds(Collections.singletonList(customerTaskId));
                List<PadTaskViewVO> padTaskViewVOList = taskService.padTaskDetailsService(taskDetailsInfoDTO);
                if (CollUtil.isNotEmpty(padTaskViewVOList)) {
                    PadTaskViewVO padTaskViewVO = padTaskViewVOList.get(0);
                    if (Objects.equals(TaskStatusConstants.SUCCESS.getStatus(), padTaskViewVO.getTaskStatus())) {
                        SyncCmdVO syncCmdVO = new SyncCmdVO();
                        syncCmdVO.setPadCode(padCode);
                        syncCmdVO.setTaskId(customerTaskId);
                        syncCmdVO.setErrorMsg(padTaskViewVO.getErrorMsg());
                        syncCmdVO.setTaskStatus(padTaskViewVO.getTaskStatus());
                        syncCmdVO.setTaskResult(REQUEST_SUCCESS);
                        syncCmdVOS.add(syncCmdVO);
                        return syncCmdVOS;
                    }
                }
            }

            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                throw new BasicException(EXECUTE_CMD_TIME_OUT_EXCEPTION);
            }
        }
        throw new BasicException(EXECUTE_CMD_TIME_OUT_EXCEPTION);
    }

    @Override
    public List<GeneratePadTaskVO> listApp(PadCodesDTO param) {
        long customerId = param.getCustomerId();
        PadCMDForwardDTO padCMDForwardDTO = new PadAppListCMDDTO().builderForwardDTO(param.getPadCodes(), PAAS);
        return GeneratePadTaskVO.builder(padTaskComponent.addPadCMDTask(customerId, param.getPadCodes(),
                LIST_INSTALLED_APP, padCMDForwardDTO));
    }

    /**
     * 批量设置pad wifi属性
     *
     * @param param
     * @return
     */
    @Override
    public List<GeneratePadTaskVO> setWifiList(SetWifiListDTO param) {
        String result = convertListToJson(param.getWifiJsonList());
        PadCMDForwardDTO padCMDForwardDTO = new PadWifiListCMDDTO().setWifiJsonList(result)
                .builderForwardDTO(param.getPadCodes(), PAAS);
        return GeneratePadTaskVO.builder(padTaskComponent.addPadCMDTask(param.getCustomerId(), param.getPadCodes(), SET_WIFI_LIST, padCMDForwardDTO));
    }

    public static String convertListToJson(List<WifiListInfo> wifiList) {
        StringBuilder result = new StringBuilder("[\n");

        for (int i = 0; i < wifiList.size(); i++) {
            WifiListInfo wifi = wifiList.get(i);
            result.append("{\n")
                    .append("\"SSID\": \"").append(wifi.getSSID()).append("\",\n")
                    .append("\"BSSID\": \"").append(wifi.getBSSID()).append("\",\n")
                    .append("\"MAC\": \"").append(wifi.getMAC()).append("\",\n")
                    .append("\"IP\": \"").append(wifi.getIP()).append("\",\n")
                    .append("\"gateway\": \"").append(wifi.getGateway()).append("\",\n")
                    .append("\"DNS1\": \"").append(wifi.getDNS1()).append("\",\n")
                    .append("\"DNS2\": \"").append(wifi.getDNS2()).append("\",\n")
                    .append("\"hessid\": ").append(wifi.getHessid()).append(",\n")
                    .append("\"anqpDomainId\": ").append(wifi.getAnqpDomainId()).append(",\n")
                    .append("\"capabilities\": \"").append(wifi.getCapabilities()).append("\",\n")
                    .append("\"level\": ").append(wifi.getLevel()).append(",\n")
                    .append("\"linkSpeed\": ").append(wifi.getLinkSpeed()).append(",\n")
                    .append("\"txLinkSpeed\": ").append(wifi.getTxLinkSpeed()).append(",\n")
                    .append("\"rxLinkSpeed\": ").append(wifi.getRxLinkSpeed()).append(",\n")
                    .append("\"frequency\": ").append(wifi.getFrequency()).append(",\n")
                    .append("\"distance\": ").append(wifi.getDistance()).append(",\n")
                    .append("\"distanceSd\": ").append(wifi.getDistanceSd()).append(",\n")
                    .append("\"channelWidth\": ").append(wifi.getChannelWidth()).append(",\n")
                    .append("\"centerFreq0\": ").append(wifi.getCenterFreq0()).append(",\n")
                    .append("\"centerFreq1\": ").append(wifi.getCenterFreq1()).append(",\n")
                    .append("\"is80211McRTTResponder\": ").append(wifi.is80211McRTTResponder()).append("\n");

            if (i < wifiList.size() - 1) {
                result.append("},\n");
            } else {
                result.append("}\n");
            }
        }

        result.append("]");
        return result.toString();
    }

    @Override
    public List<GeneratePadTaskVO> restartService(RestartDTO param) {
        long customerId = param.getCustomerId();

        if (CollUtil.isEmpty(param.getPadCodes()) && CollUtil.isEmpty(param.getGroupIds())) {
            throw new BasicException(PARAMETER_EXCEPTION);
        }
        if (redisService.isAdmin(customerId)) {
            customerId = 0L;
        }
        List<Pad> padList = padMapper.getPadByGroupIds(param.getPadCodes(), param.getGroupIds(), customerId);
        if (CollectionUtils.isEmpty(padList)) {
            throw new BasicException(PAD_CODE_NOT_EXIST);
        }
        customerId = param.getCustomerId();
        PadRestartTaskQueueBO padRestartTaskQueueBO = new PadRestartTaskQueueBO();
        String oprBy = Optional.of(param).map(RestartDTO::getOprBy).orElse(String.valueOf(customerId));
        padRestartTaskQueueBO.setOprBy(oprBy);
        PadTaskBO padTaskBO = padTaskComponent.addPadTaskWithPaas(customerId,
                padList.stream().map(Pad::getPadCode).distinct().collect(Collectors.toList()), RESTART,
                JSON.toJSONString(padRestartTaskQueueBO), param.getTaskSource());

        List<GeneratePadTaskVO> result = GeneratePadTaskVO.builder(padTaskBO);
        try {
            // 重启换IP
            if (param.getChangeIpFlag()) {
                CompletableFuture.runAsync(() -> {
                    padList.parallelStream().forEach(pad -> {
                        ArmServer serverDetail = armServerService.getArmServerDetail(pad.getArmServerCode());
                        if (Objects.isNull(serverDetail)) {
                            return;
                        }
                        // 指定实例
                        // 任务能正常提交,实例肯定会有服务分组
                        HashMap<String, Object> paramMap = new HashMap<>();
                        paramMap.put("instanceIP", pad.getPadIp());
                        paramMap.put("clusterNumber", serverDetail.getClusterCode());
                        paramMap.put("key", "mswpZTKjaLix92O0JQxDsmClfGZlS6WO0IV6U5vy7wU7I6GAImBYTU65zHZT3bja");
                        String switchResult = HttpUtil.post(getEdgeUrlPrefix(pad.getPadCode()) + "/api/cms-go/publicIP/switch",
                                JSONUtil.toJsonStr(paramMap));
                        log.info("PadServiceImpl.restartService run_publicIP switchResult:{} ,padCode:{}", switchResult,
                                pad.getPadCode());
                    });
                }, SWITCH_PUBLIC_IP_EXECUTOR);
            }
        } catch (Exception e) {
            log.error("PadServiceImpl.restartService run_publicIP error:{}", e.getMessage(), e);
        }

        return result;
    }

    @Override
    public List<GeneratePadTaskVO> restService(ResetDTO param) {
        if (CollUtil.isEmpty(param.getPadCodes()) && CollUtil.isEmpty(param.getGroupIds())) {
            throw new BasicException(PARAMETER_EXCEPTION);
        }
        long customerId = param.getCustomerId();
        if (redisService.isAdmin(customerId)) {
            customerId = 0L;
        }
        List<Pad> padList = padMapper.getPadByGroupIds(param.getPadCodes(), param.getGroupIds(), customerId);
        if (CollectionUtils.isEmpty(padList)) {
            throw new BasicException(PAD_CODE_NOT_EXIST);
        }
        customerId = param.getCustomerId();
        PadRestartTaskQueueBO padRestartTaskQueueBO = new PadRestartTaskQueueBO();
        String oprBy = Optional.of(param).map(ResetDTO::getOprBy).orElse(String.valueOf(customerId));
        padRestartTaskQueueBO.setOprBy(oprBy);
        PadTaskBO padTaskBO = padTaskComponent.addPadTaskWithPaas(customerId,
                padList.stream().map(Pad::getPadCode).distinct().collect(Collectors.toList()), RESET,
                JSON.toJSONString(padRestartTaskQueueBO),param.getTaskSource());
        // padMapper.update(new UpdateWrapper<Pad>().in("pad_code",
        // padCodes).set("adb_open_status", 0));
        try {
            // 更换IP失败丢弃都没有关系,每次更换都不一定成功
            CompletableFuture.runAsync(() -> padList.parallelStream().forEach(pad -> {
                ArmServer serverDetail = armServerService.getArmServerDetail(pad.getArmServerCode());
                if (Objects.isNull(serverDetail)) {
                    return;
                }
                // 指定实例
                // 任务能正常提交,实例肯定会有服务分组
                HashMap<String, Object> paramMap = new HashMap<>();
                paramMap.put("instanceIP", pad.getPadIp());
                paramMap.put("clusterNumber", serverDetail.getClusterCode());
                paramMap.put("key", "mswpZTKjaLix92O0JQxDsmClfGZlS6WO0IV6U5vy7wU7I6GAImBYTU65zHZT3bja");
                String resetResult = HttpUtil.post(getEdgeUrlPrefix(pad.getPadCode()) + "/api/cms-go/publicIP/reset",
                        JSONUtil.toJsonStr(paramMap));
                log.info("PadServiceImpl.restartService  run_publicIP padCode:{} resetResult:{}", pad.getPadCode(),
                        resetResult);
            }), SWITCH_PUBLIC_IP_EXECUTOR);
        } catch (Exception e) {
            log.error("PadServiceImpl.restService run_publicIP error:{}", e.getMessage(), e);
        }

        return GeneratePadTaskVO.builder(padTaskBO);
    }

    private String getEdgeUrlPrefix(String padCode) {
        String edgeClusterCode = edgeClusterMapper.selectEdgeClusterCodeByPadCodeSingle(padCode);
        String urlPrefix = edgeClusterConfigurationService.getEdgeClusterConfigurationByKey(edgeClusterCode, EdgeClusterConfigurationEnum.EDGE_API_BASE_URL);

        return urlPrefix;
    }

    @Override
    public List<GeneratePadTaskInfoVO> upgradeImageService(UpgradeImageDTO param, SourceTargetEnum sourceTarget) {
        List<String> uniquePadCodes = param.getPadCodes().stream().distinct().collect(Collectors.toList());
        param.setPadCodes(uniquePadCodes);
        long customerId = param.getCustomerId();
        LambdaQueryWrapper<CustomerUploadImage> queryWrapper = new QueryWrapper<CustomerUploadImage>().lambda();
        if (!SourceTargetEnum.ADMIN_SYSTEM.equals(param.getTaskSource())) {
            queryWrapper.and(wrapper -> wrapper.eq(CustomerUploadImage::getCustomerId, customerId).or()
                    .isNull(CustomerUploadImage::getCustomerId));
        }

        queryWrapper.eq(CustomerUploadImage::getUniqueId, param.getImageId())
                .eq(CustomerUploadImage::getStatus, TWO)
                .eq(CustomerUploadImage::getDeleteFlag, ZERO);
        CustomerUploadImage customerUploadImage = customerUploadImageMapper.selectOne(queryWrapper);
        if (isEmpty(customerUploadImage)) {
            throw new BasicException(IMAGE_NOT_EXIST);
        }

        // 云真机只能13升13 14升14
        checkAndroidVersion(uniquePadCodes, param.getImageId(), null,param.getWipeData());

        Pad pad = padMapper.selectOne(new QueryWrapper<Pad>().lambda().eq(Pad::getPadCode, uniquePadCodes.get(ZERO)));
        if (pad.getSocModel().startsWith("QAC")) {
            param.setImageId(customerUploadImage.getImageName());
        }

        PadUpgradeImageTaskQueueBO taskQueueBO = new PadUpgradeImageTaskQueueBO();
        taskQueueBO.setSourceTarget(SourceTargetEnum.PAAS.getCode());
        taskQueueBO.setImageId(param.getImageId());
        taskQueueBO.setWipeData(param.getWipeData());
        String oprBy = Optional.of(param).map(UpgradeImageDTO::getOprBy).orElse(String.valueOf(customerId));
        taskQueueBO.setOprBy(oprBy);

        Consumer<AddPadTaskDTO> padTaskConsumer = addPadTaskDTO -> {
            addPadTaskDTO.setCreateBy(oprBy);
            addPadTaskDTO.setLastImageId(pad.getImageId());
            addPadTaskDTO.setImageId(param.getImageId());
            addPadTaskDTO.setWipeData(param.getWipeData());
        };
        PadTaskBO padTaskBO = padTaskComponent.addPadTask(customerId, param.getPadCodes(),
                UPGRADE_IMAGE, padTaskConsumer, JSON.toJSONString(taskQueueBO), sourceTarget);
        // padMapper.update(new UpdateWrapper<Pad>().in("pad_code",
        // uniquePadCodes).set("adb_open_status", 0));
        return GeneratePadTaskInfoVO.builder(padTaskBO);
    }

    @Override
    public Pad getPadByPadCode(String padCode) {
        return padMapper.selectOne(new QueryWrapper<Pad>().eq("pad_code", padCode));
    }

    public List<Pad> getPadListByPadCode(List<String> padCodeList) {
        return padMapper.selectList(new QueryWrapper<Pad>().in("pad_code", padCodeList));
    }

    @Override
    public List<Pad> extractedPad(VirtualizeDeviceDTO param, ResourceSpecification resourceSpecification,
                                  ScreenLayout screenLayout, VirtualizeDeviceInfoVO device, List<String> ips) {
        Integer padNumber = resourceSpecification.getPadNumber();
        List<PadInfoVO> padInfoVOList = null;
        // 实例编号新规则 是随机数 因此这里不必再获取上一次分配的记录了
        if (!createPadCodeNew) {
            padInfoVOList = padMapper.selectPadInfosByDeviceId(device.getDeviceId(), padNumber);
        }

        // 获取被使用的内网ip集合
        List<String> usedPadIps = padMapper.selectUsePadIps(ips);

        // 获取可用ip加个redis锁
        List<String> unusedIps = new ArrayList<>();
        if (CollUtil.isNotEmpty(ips)) {
            for (String usedPadIp : ips) {
                String key = RedisKeyPrefix.PAD_IP_LOCK + usedPadIp;
                if (!usedPadIps.contains(usedPadIp) && Objects.isNull(redisService.getCacheObject(key))) {
                    redisService.setCacheObject(key, usedPadIp, RedisKeyTime.minute_5, TimeUnit.MINUTES);
                    unusedIps.add(usedPadIp);
                }
                if (unusedIps.size() == padNumber.intValue()) {
                    break;
                }
            }
            // unusedIps = ips.stream().filter(ip ->
            // !usedPadIps.contains(ip)).collect(Collectors.toList());
        }
        if (unusedIps.size() < padNumber) {
            throw new BasicException(PAD_IP_NOT_ENOUGH);
        }
        List<Pad> virtualizePads = new ArrayList<>();
        int padSn = ZERO;
        int index = ZERO;
        if (CollUtil.isNotEmpty(padInfoVOList)) {
            for (PadInfoVO padInfo : padInfoVOList) {
                if (padInfo.getStatus().equals(-1)) {
                    Pad pad = getUpdatePad(param, resourceSpecification, screenLayout, device, padInfo);
                    pad.setPadIp(unusedIps.get(index));
                    pad.setPadOutCode(padInfo.getPadCode());
                    pad.setCloudVendorType(ARM_CLOUD_VENDOR_TYPE);
                    padMapper.updateById(pad);
                    pad.setPadCode(padInfo.getPadCode());

                    PadStatus padStatus = new PadStatus();
                    padStatus.setPadStatus(NOT_READY);
                    padStatus.setUpdateTime(new Date());
                    padStatusMapper.update(padStatus, new QueryWrapper<PadStatus>().eq("pad_code", pad.getPadCode()));
                    // 新增实例操作日志
                    recordPadOperLog("virtualize", MODIFY.getCode(), "virtualize", param.getSourceCode(),
                            param.getOprBy(), JSON.toJSONString(pad));
                    virtualizePads.add(pad);
                    index++;
                }
                padSn = padInfo.getPadSn();
            }
        }
        padSn = padSn + ONE;
        for (int i = padSn; i <= padNumber; i++) {
            Pad pad = getAddPad(param, resourceSpecification, screenLayout, device, unusedIps, index, i);
            virtualizePads.add(pad);
            index++;
        }

        virtualizePads.forEach(this::setMacInfo);
        updatePadType(virtualizePads, param.getPadType());
        return virtualizePads;
    }

    private void updatePadType(List<Pad> virtualizePads, String padType) {
        if (CollectionUtils.isEmpty(virtualizePads)) {
            return;
        }
        List<Long> padIds = virtualizePads.stream().map(Pad::getId).collect(Collectors.toList());
        padMapper.updateTypeByIds(padIds, padType);
    }


    public List<Pad> extractedPadNet(NetWorkVirtualizeDTO param, ResourceSpecification resourceSpecification, ScreenLayout screenLayout) {
        List<Pad> virtualizePads = new ArrayList<>();
        int index = ZERO;
        //每次都新建
        for (int i = 0; i < param.getNumber(); i++) {

            Pad pad = getAddPadNet(param, resourceSpecification, screenLayout, index, i + 1);
            virtualizePads.add(pad);
            index++;
        }
        virtualizePads.forEach(this::setMacInfo);
        return virtualizePads;
    }

    private void setMacInfo(Pad pad) {
        int try_count = 0;
        do {
            if (try_count > 30) {
                throw new BasicException(PAD_MAC_ADDR_GENERATE_FAIL);
            }
            String mac = MACUtils.generateMacAddress();
            int updateSize = padMapper.updateMacById(pad.getId(), mac);
            try_count++;
            if (updateSize <= 0) {
                continue;
            }

            PadMacLog padMacLog = new PadMacLog();
            padMacLog.setPadCode(pad.getPadCode());
            padMacLog.setMac(mac);
            padMacLogMapper.insert(padMacLog);
            pad.setMac(mac);
            return;
        } while (true);
    }

    /**
     * 记录实例操作日志
     *
     * @param title        操作模块
     * @param businessType 业务类型
     * @param method       请求方法
     * @param sourceType   来源
     * @param operName     操作人员
     * @param param        参数
     */
    private void recordPadOperLog(String title, Integer businessType, String method, String sourceType, String operName,
                                  String param) {
        PadOperLog padOperLog = new PadOperLog();
        padOperLog.setTitle(title);
        padOperLog.setBusinessType(businessType);
        padOperLog.setMethod(method);
        padOperLog.setSourceType(sourceType);
        padOperLog.setOperName(operName);
        padOperLog.setParam(param);
        padOperLog.setOperTime(new Date());
        padOperLogMapper.insert(padOperLog);
    }

    private Pad getAddPad(VirtualizeDeviceDTO param, ResourceSpecification resourceSpecification,
                          ScreenLayout screenLayout, VirtualizeDeviceInfoVO device, List<String> unusedIps, int index, int i) {
        Pad pad = new Pad();
        String code = null;

        if (createPadCodeNew) {
            // 生成30次padCode, 取一个不重复的
            int count = 0;
            do {
                if (count > 30) {
                    throw new BasicException(PAD_CODE_GENERATE_FAIL);
                }
                code = generatePadCode(device.getDeviceCode());
                if (padMapper.existPadByCode(code) == 0) {
                    break;
                }
                count++;
            } while (true);
        } else {
            code = device.getDeviceCode().substring(ZERO, device.getDeviceCode().length() - ONE)
                    + Constants.PAD_SUFFIX[i - 1];
        }
        pad.setPadCode(code);
        pad.setPadOutCode(code);
        pad.setCloudVendorType(ARM_CLOUD_VENDOR_TYPE);
        pad.setPadIp(unusedIps.get(index));
        pad.setDeviceLevel(resourceSpecification.getSpecificationCode());
        pad.setCustomerId(device.getCustomerId());
        pad.setGroupId(Long.valueOf(ZERO));
        pad.setStatus(ZERO);
        pad.setImageId(param.getImageId());
        pad.setPadSn(i);
        pad.setOnline(ZERO);
        pad.setStatus(ZERO);
        pad.setStreamStatus(ZERO);
        if (isNotEmpty(param.getStreamType())) {
            pad.setStreamType(param.getStreamType());
        }
        /* pad.setStreamType(VOLCENGINE.getIntValue()); */
        pad.setCreateBy("virtualize:" + param.getOprBy());
        log.info("PadServiceImpl.getAddPad dns:{}", param.getDns());
        pad.setDns(StringUtils.isBlank(param.getDns())
                ? configurationMapper.selectValueByKey(INSTANCE_DEFAULT_DNS)
                : param.getDns());
        pad.setCreateTime(new Date());
        pad.setSocModel(resourceSpecification.getSocModel());
        pad.setCpu(param.getIsolateCpu() ? resourceSpecification.getCpu() : MINUS_ONE);
        pad.setMemory(param.getIsolateMemory() ? resourceSpecification.getMemory() : MINUS_ONE);
        pad.setStorage(param.getIsolateStorage() ? resourceSpecification.getStorage() : MINUS_ONE);
        pad.setScreenLayoutCode(screenLayout.getCode());
        pad.setArmServerCode(device.getArmServerCode());
        pad.setRealPhoneTemplateId(param.getRealPhoneTemplateId());
        pad.setType(Objects.isNull(param.getRealPhoneTemplateId()) ? PadConstants.Type.VIRTUAL.getValue() : PadConstants.Type.REAL.getValue());
        padMapper.insert(pad);

        PadStatus padStatus = new PadStatus();
        padStatus.setPadCode(pad.getPadCode());
        padStatus.setPadOutCode(code);
        padStatus.setPadStatus(NOT_READY);
        padStatusMapper.insert(padStatus);

        DevicePad devicePad = new DevicePad();
        devicePad.setDeviceId(device.getDeviceId());
        devicePad.setPadId(pad.getId());
        devicePadMapper.insert(devicePad);

        // 单控房间
        PadRoom crmRoom = new PadRoom();
        crmRoom.setPadCode(pad.getPadCode());
        crmRoom.setStatus(ONE);
        crmRoom.setRoomCode(pad.getPadCode().replace(AC, CRM));
        crmRoom.setType(ONE);
        crmRoom.setCreateBy("virtualize:" + param.getOprBy());
        padRoomMapper.insert(crmRoom);

        // 群控房间
        PadRoom srmRoom = new PadRoom();
        srmRoom.setPadCode(pad.getPadCode());
        srmRoom.setStatus(ONE);
        srmRoom.setRoomCode(pad.getPadCode().replace(AC, SRM));
        srmRoom.setType(TWO);
        srmRoom.setCreateBy("virtualize:" + param.getOprBy());
        padRoomMapper.insert(srmRoom);

        // 新增实例操作日志
        recordPadOperLog("virtualize", ADD.getCode(), "virtualize", param.getSourceCode(), param.getOprBy(),
                JSON.toJSONString(pad));
        return pad;
    }

    @Resource
    NetStorageResUnitService netStorageResUnitService;

    private Pad getAddPadNet(NetWorkVirtualizeDTO param, ResourceSpecification resourceSpecification, ScreenLayout screenLayout, int index, int i) {
        Pad pad = new Pad();
        String code = calculationNetPadCode();
        if (StringUtils.isEmpty(code)) {
            throw new BasicException("网存实例数量过多，请稍后再试");
        }
        pad.setPadCode(code);
        pad.setPadOutCode(code);
        pad.setCloudVendorType(ARM_CLOUD_VENDOR_TYPE);
        pad.setDeviceLevel(resourceSpecification.getSpecificationCode());
        pad.setCustomerId(param.getCustomerId());
        pad.setGroupId(Long.valueOf(ZERO));
        pad.setStatus(ZERO);
        String dns =param.getDns();
        //没有传dns,拿配置中的默认值
        if(StringUtils.isEmpty(dns)){
            String value = edgeClusterConfigurationService.getEdgeClusterConfigurationByKey(param.getClusterCode(), EdgeClusterConfigurationEnum.CLUSTER_DNS_DEFAULT_SERVERS);
            if (StrUtil.isNotBlank(value)) {
                dns = value;
            }
        };
        pad.setDns(dns);
        //转换成b
        pad.setDataSize(param.getStorageSize() * 1024L * 1024 * 1024);

        pad.setImageId(param.getImageId());
        pad.setPadSn(i);
        //网存Id拼接实例
        String netStorageResUnitCode = IdGeneratorUtils.generateStorageId() + "-" + code;
        pad.setNetStorageResId(netStorageResUnitCode);
        pad.setNetStorageResSize((long) param.getStorageSize());
        pad.setOnline(ZERO);
        //网存打成可用状态
        pad.setStatus(ONE);
        pad.setStreamStatus(ZERO);
        Integer streamType = customerConfigMapper.getStreamTypeByCustomerId(param.getCustomerId());
        pad.setStreamType(streamType);
        if (isNotEmpty(param.getStreamType()) && isNotEmpty(pad.getStreamType())) {
            pad.setStreamType(param.getStreamType());
        }
        /*        pad.setStreamType(VOLCENGINE.getIntValue());*/
        pad.setCreateBy("virtualize:" + param.getOprBy());
        pad.setCreateTime(new Date());
        pad.setSocModel(resourceSpecification.getSocModel());
        // CPU不隔离
        pad.setCpu(MINUS_ONE);
        pad.setMemory(param.getIsolateMemory() ? resourceSpecification.getMemory() : MINUS_ONE);
        pad.setStorage(param.getIsolateStorage() ? resourceSpecification.getStorage() : MINUS_ONE);
        pad.setScreenLayoutCode(screenLayout.getCode());
        //网存实例不需要服务器
//        pad.setArmServerCode(server.getArmServerCode());
        pad.setNetStorageResFlag(1);
        pad.setClusterCode(param.getClusterCode());
        pad.setRealPhoneTemplateId(param.getRealPhoneTemplateId());
        //没有adi就是虚拟实例，有adi就是真实实例
        pad.setType(Objects.isNull(param.getRealPhoneTemplateId()) ? PadConstants.Type.VIRTUAL.getValue() : PadConstants.Type.REAL.getValue());
        padMapper.insert(pad);
        //写入网存信息
        NetStorageResUnit netStorageResUnit = new NetStorageResUnit();
        netStorageResUnit.setCustomerId(pad.getCustomerId());
        netStorageResUnit.setPadCode(pad.getPadCode());
        netStorageResUnit.setCreateBy("virtualize:" + param.getOprBy());
        netStorageResUnit.setNetStorageResUnitCode(netStorageResUnitCode);
        netStorageResUnit.setNetStorageResUnitSize(pad.getNetStorageResSize());
        //初始化值为0
        netStorageResUnit.setNetStorageResUnitUsedSize("0");
        netStorageResUnit.setClusterCode(param.getClusterCode());
        netStorageResUnit.setCreateTime(new Date());
//        netStorageResUnit.setcustomerId(param.getCustomerId());
        netStorageResUnit.setUpdateTime(new Date());
        netStorageResUnitService.save(netStorageResUnit);
        PadStatus padStatus = new PadStatus();
        padStatus.setPadCode(pad.getPadCode());
        padStatus.setPadOutCode(code);
        //网存实例创建默认关机
        padStatus.setPadStatus(PadStatusConstant.OFF);
        padStatusMapper.insert(padStatus);

        //写入网存实例创建信息
        NetStoragePadUnitDetail unitDetail = new NetStoragePadUnitDetail();
        unitDetail.setPadCode(pad.getPadCode());
        unitDetail.setClusterCode(pad.getClusterCode());
        unitDetail.setDeviceAndroidProp(param.getDeviceAndroidProps());
        unitDetail.setNetStorageResApplySize(String.valueOf(pad.getNetStorageResSize()));
        unitDetail.setRealPhoneTemplateId(pad.getRealPhoneTemplateId());
        unitDetail.setNetStorageResUnitCode(netStorageResUnitCode);
        netStoragePadUnitDetailMapper.insert(unitDetail);
        //TODO  网存实例刚创建不需要绑定板卡关系,因为不存在板卡
//        DevicePad devicePad = new DevicePad();
//        devicePad.setDeviceId(device.getDeviceId());
//        devicePad.setPadId(pad.getId());
//        devicePadMapper.insert(devicePad);

        //单控房间
        PadRoom crmRoom = new PadRoom();
        crmRoom.setPadCode(pad.getPadCode());
        crmRoom.setStatus(ONE);
        crmRoom.setRoomCode(pad.getPadCode().replace(AC, CRM));
        crmRoom.setType(ONE);
        crmRoom.setCreateBy("virtualize:" + param.getOprBy());
        padRoomMapper.insert(crmRoom);

        //群控房间
        PadRoom srmRoom = new PadRoom();
        srmRoom.setPadCode(pad.getPadCode());
        srmRoom.setStatus(ONE);
        srmRoom.setRoomCode(pad.getPadCode().replace(AC, SRM));
        srmRoom.setType(TWO);
        srmRoom.setCreateBy("virtualize:" + param.getOprBy());
        padRoomMapper.insert(srmRoom);
        //新增实例操作日志
        recordPadOperLog("virtualize", ADD.getCode(), "virtualize", param.getSourceTarget().getCode(), param.getOprBy(), JSON.toJSONString(pad));
        return pad;
    }

    private String calculationNetPadCode() {
        int numberOfCycles = 0;
        while (numberOfCycles <= 30) {
            String code = IdGeneratorUtils.generateStoragePadCodeId();
            Pad pad = padMapper.selectOne(new LambdaQueryWrapper<Pad>().eq(Pad::getPadCode, code));
            if (Objects.isNull(pad)) {
                return code;
            }
            numberOfCycles++;
        }
        return StringUtils.EMPTY;
    }


    private Pad getUpdatePad(VirtualizeDeviceDTO param, ResourceSpecification resourceSpecification,
                             ScreenLayout screenLayout, VirtualizeDeviceInfoVO device, PadInfoVO padInfo) {
        Pad pad = new Pad();
        pad.setId(padInfo.getPadId());
        pad.setDeviceLevel(resourceSpecification.getSpecificationCode());
        pad.setCustomerId(device.getCustomerId());
        pad.setGroupId(Long.valueOf(ZERO));
        pad.setStatus(ZERO);
        pad.setImageId(param.getImageId());
        pad.setOnline(ZERO);
        pad.setStatus(ZERO);
        pad.setStreamStatus(ZERO);
        if (isNotEmpty(param.getStreamType())) {
            pad.setStreamType(param.getStreamType());
        }
        // pad.setStreamType(VOLCENGINE.getIntValue());
        pad.setUpdateBy("virtualize:" + param.getOprBy());
        log.info("PadServiceImpl.getAddPad dns:{}", param.getDns());
        pad.setDns(StringUtils.isBlank(param.getDns())
                ? configurationMapper.selectValueByKey(INSTANCE_DEFAULT_DNS)
                : param.getDns());
        pad.setUpdateTime(new Date());
        pad.setSocModel(resourceSpecification.getSocModel());
        pad.setCpu(param.getIsolateCpu() ? resourceSpecification.getCpu() : MINUS_ONE);
        pad.setMemory(param.getIsolateMemory() ? resourceSpecification.getMemory() : MINUS_ONE);
        pad.setStorage(param.getIsolateStorage() ? resourceSpecification.getStorage() : MINUS_ONE);
        pad.setScreenLayoutCode(screenLayout.getCode());
        pad.setArmServerCode(device.getArmServerCode());
        return pad;
    }

    @Override
    public PadPropertiesVO padPropertiesService(PadPropertiesDTO dto) {

        PadProperties properties = padPropertiesMapper.selectPropertiesByCode(dto.getPadCode());
        if (Objects.isNull(properties)) {
            throw new BasicException(INSTANCE_PROPERTIES_NOT_SYNCHRONIZED);
        }

        List<String> modemPropertiesKeys = padPropertiesKeyService.getPropertiesKeyByType(ONE);
        List<String> systemPropertiesKeys = padPropertiesKeyService.getPropertiesKeyByType(NumberConsts.TWO);
        List<String> settingPropertiesKeys = padPropertiesKeyService.getPropertiesKeyByType(NumberConsts.THREE);
        List<String> oaIdPropertiesKeys = padPropertiesKeyService.getPropertiesKeyByType(NumberConsts.FOUR);

        List<PadPropertiesSub> modemPropertiesListVO = new ArrayList<>();
        List<PadPropertiesSub> systemPropertiesListVO = new ArrayList<>();
        List<PadPropertiesSub> settingPropertiesListVO = new ArrayList<>();
        List<PadPropertiesSub> oaIdPropertiesListVO = new ArrayList<>();

        PadPropertiesVO padPropertiesVO = new PadPropertiesVO();
        padPropertiesVO.setPadCode(dto.getPadCode());
        padPropertiesVO.setModemPropertiesList(modemPropertiesListVO);
        padPropertiesVO.setSystemPropertiesList(systemPropertiesListVO);
        padPropertiesVO.setSettingPropertiesList(settingPropertiesListVO);
        padPropertiesVO.setOaidPropertiesList(oaIdPropertiesListVO);

        List<PadPropertiesSub> propertiesValues = properties.getPropertiesValues();
        if (CollectionUtils.isEmpty(propertiesValues)) {
            return padPropertiesVO;
        }
        propertiesValues.forEach(padPropertiesSub -> {
            if (modemPropertiesKeys.contains(padPropertiesSub.getPropertiesName())) {
                modemPropertiesListVO.add(padPropertiesSub);

            } else if (systemPropertiesKeys.contains(padPropertiesSub.getPropertiesName())) {
                systemPropertiesListVO.add(padPropertiesSub);

            } else if (settingPropertiesKeys.contains(padPropertiesSub.getPropertiesName())) {
                settingPropertiesListVO.add(padPropertiesSub);

            } else if (oaIdPropertiesKeys.contains(padPropertiesSub.getPropertiesName())) {
                oaIdPropertiesListVO.add(padPropertiesSub);
            }
        });

        return padPropertiesVO;
    }

    @Override
    public List<PadPropertiesVO> batchPadProperties(BatchPadPropertiesDTO dto) {

        List<PadProperties> propertiesList = padPropertiesMapper.findPropertiesByCodes(dto.getPadCodes());
        if (Objects.isNull(propertiesList)) {
            throw new BasicException(INSTANCE_PROPERTIES_NOT_SYNCHRONIZED);
        }
        List<String> modemPropertiesKeys = padPropertiesKeyService.getPropertiesKeyByType(ONE);
        List<String> systemPropertiesKeys = padPropertiesKeyService.getPropertiesKeyByType(NumberConsts.TWO);
        List<String> settingPropertiesKeys = padPropertiesKeyService.getPropertiesKeyByType(NumberConsts.THREE);
        List<String> oaIdPropertiesKeys = padPropertiesKeyService.getPropertiesKeyByType(NumberConsts.FOUR);

        List<PadPropertiesVO> padPropertiesVOList = new ArrayList<>();
        for (PadProperties padProperties : propertiesList) {
            PadPropertiesVO padPropertiesVO = new PadPropertiesVO();
            padPropertiesVO.setPadCode(padProperties.getPadCode());
            if (!CollectionUtils.isEmpty(padProperties.getPropertiesValues())) {
                List<PadPropertiesSub> modemPropertiesListVO = new ArrayList<>();
                List<PadPropertiesSub> systemPropertiesListVO = new ArrayList<>();
                List<PadPropertiesSub> settingPropertiesListVO = new ArrayList<>();
                List<PadPropertiesSub> oaIdPropertiesListVO = new ArrayList<>();
                for (PadPropertiesSub padPropertiesSub : padProperties.getPropertiesValues()) {
                    if (modemPropertiesKeys.contains(padPropertiesSub.getPropertiesName())) {
                        modemPropertiesListVO.add(padPropertiesSub);
                    } else if (systemPropertiesKeys.contains(padPropertiesSub.getPropertiesName())) {
                        systemPropertiesListVO.add(padPropertiesSub);
                    } else if (settingPropertiesKeys.contains(padPropertiesSub.getPropertiesName())) {
                        settingPropertiesListVO.add(padPropertiesSub);
                    } else if (oaIdPropertiesKeys.contains(padPropertiesSub.getPropertiesName())) {
                        oaIdPropertiesListVO.add(padPropertiesSub);
                    }
                }
                padPropertiesVO.setModemPropertiesList(modemPropertiesListVO);
                padPropertiesVO.setSystemPropertiesList(systemPropertiesListVO);
                padPropertiesVO.setSettingPropertiesList(settingPropertiesListVO);
                padPropertiesVO.setOaidPropertiesList(oaIdPropertiesListVO);
            }
            padPropertiesVOList.add(padPropertiesVO);
        }

        return padPropertiesVOList;
    }

    @Override
    public List<GeneratePadTaskVO> updatePadPropertiesService(UpdatePadPropertiesDTO param) {
        long customerId = param.getCustomerId();
        PadCMDForwardDTO padCMDForwardDTO = new UpdatePropertiesCMDDTO()
                .setModemPersistPropertiesList(param.getModemPersistPropertiesList())
                .setModemPropertiesList(param.getModemPropertiesList())
                .setSystemPersistPropertiesList(param.getSystemPersistPropertiesList())
                .setSystemPropertiesList(param.getSystemPropertiesList())
                .setSettingPropertiesList(param.getSettingPropertiesList())
                .setOaidPropertiesList(param.getOaidPropertiesList())
                .builderForwardDTO(param.getPadCodes(), PAAS);
        return GeneratePadTaskVO.builder(padTaskComponent.addPadCMDTask(customerId, param.getPadCodes(),
                UPDATE_PAD_PROPERTIES, padCMDForwardDTO));
    }

    @Override
    public List<GeneratePadTaskVO> newPads(NewPadsDTO param) {
        long customerId = param.getCustomerId();
        String oprBy = Optional.of(param).map(NewPadsDTO::getOprBy).orElse(String.valueOf(customerId));
        SourceTargetEnum sourceTarget = Optional.of(param).map(NewPadsDTO::getTaskSource).orElse(SourceTargetEnum.PAAS);
        PadCMDForwardDTO padCMDForwardDTO = new UpdatePropertiesCMDDTO()
                .builderForwardNewPadDTO(param.getPadModels(), sourceTarget).setOprBy(oprBy);

        List<String> padCodes = param.getPadModels().stream().map(PadModelDTO::getPadCode).collect(Collectors.toList());
        return GeneratePadTaskVO.builder(
                padTaskComponent.addPadCMDTask(customerId, padCodes, UPDATE_PAD_PROPERTIES, padCMDForwardDTO));
    }

    @Override
    public List<GeneratePadTaskVO> triggeringBlackListService(TriggeringBlackDTO param) {
        Long customerId = param.getCustomerId();
        List<String> padCodes = padMapper.getPadCodesByDeviceLevel(param.getPadCodes(), param.getPadGrade(),
                customerId);
        List<String> blacklists = appBlackMapper.selectBlackAppPkgList(customerId, param.getPadGrade());

        // 新黑白名单中各实例自定义黑名单 key为黑白名单id value为包名黑名单列表
        Map<Long, List<String>> padBlackMap = null;
        // 新黑白名单实例和黑白名单对应关系 key为实例编号 value为黑白名单id
        Map<String, List<Long>> padAppClassifyMap = null;
        if (param.getIsMergeAppClassifyList() == null || !param.getIsMergeAppClassifyList()
                || param.getIsNewApi() == null || !param.getIsNewApi()) {
            // 不合并新黑白名单或者老接口进来 则按照原逻辑进行校验
            if (CollUtil.isEmpty(blacklists)) {
                throw new BasicException(BLACKLIST_NOT_EXIST);
            }

            if (CollUtil.isEmpty(padCodes)) {
                throw new BasicException(PAD_CODE_NOT_EXIST);
            }
        } else {
            // 合并新黑白名单 则按新逻辑
            padCodes = param.getPadCodes();
            if (param.getApplyAllInstances() == null || !param.getApplyAllInstances()) {
                // 先查出这批padCode下所有的黑名单id
                List<CustomerAppClassifyPadRelation> customerAppClassifyPadRelationList = customerAppClassifyPadRelationMapper
                        .selectAppClassifyPadListByType(param.getCustomerId(), padCodes, 2);
                if (CollUtil.isNotEmpty(customerAppClassifyPadRelationList)) {
                    padAppClassifyMap = new HashMap<>();
                    Set<Long> appClassifyIds = new HashSet<>();
                    for (CustomerAppClassifyPadRelation customerAppClassifyPadRelation : customerAppClassifyPadRelationList) {
                        List<Long> padCodeAppClassifyIds = padAppClassifyMap
                                .get(customerAppClassifyPadRelation.getPadCode());
                        if (padCodeAppClassifyIds == null) {
                            padCodeAppClassifyIds = new ArrayList<>();
                            padAppClassifyMap.put(customerAppClassifyPadRelation.getPadCode(), padCodeAppClassifyIds);
                        }
                        padCodeAppClassifyIds.add(customerAppClassifyPadRelation.getAppClassifyId());
                        appClassifyIds.add(customerAppClassifyPadRelation.getAppClassifyId());
                    }
                    // 用这些黑名单id查出所有关联应用
                    List<CustomerAppClassifyRelation> customerAppClassifyRelationList = customerAppClassifyRelationMapper
                            .selectList(new QueryWrapper<>(CustomerAppClassifyRelation.class)
                                    .in("app_classify_id", appClassifyIds));
                    if (CollUtil.isNotEmpty(customerAppClassifyRelationList)) {
                        padBlackMap = new HashMap<>();
                        for (CustomerAppClassifyRelation customerAppClassifyRelation : customerAppClassifyRelationList) {
                            List<String> pckNames = padBlackMap.get(customerAppClassifyRelation.getAppClassifyId());
                            if (pckNames == null) {
                                pckNames = new ArrayList<>();
                                padBlackMap.put(customerAppClassifyRelation.getAppClassifyId(), pckNames);
                            }
                            pckNames.add(customerAppClassifyRelation.getPackageName());
                        }
                    }
                }
            } else {
                // 直接取该客户下所有白名单关联的应用包名
                List<CustomerAppClassify> customerAppClassifies = customerAppClassifyMapper
                        .selectList(new QueryWrapper<>(CustomerAppClassify.class)
                                .eq("customer_id", customerId)
                                .eq("classify_type", 2)
                                .eq("delete_flag", 0));
                if (CollUtil.isNotEmpty(customerAppClassifies)) {
                    List<Long> appClassifyIds = customerAppClassifies.stream().map(CustomerAppClassify::getId)
                            .collect(Collectors.toList());
                    List<CustomerAppClassifyRelation> customerAppClassifyRelationList = customerAppClassifyRelationMapper
                            .selectList(new QueryWrapper<>(CustomerAppClassifyRelation.class)
                                    .select("package_name")
                                    .in("app_classify_id", appClassifyIds)
                                    .eq("customer_id", customerId));
                    if (CollUtil.isNotEmpty(customerAppClassifyRelationList)) {
                        if (blacklists == null) {
                            blacklists = new ArrayList<>();
                        }
                        Set<String> pckNames = customerAppClassifyRelationList.stream()
                                .map(CustomerAppClassifyRelation::getPackageName).collect(Collectors.toSet());
                        blacklists.addAll(pckNames);
                    }
                }
            }
        }

        /*
         * if(CollUtil.isEmpty(blacklists) && CollUtil.isEmpty(padBlackMap)){
         * throw new BasicException(NEW_BLACKLIST_NOT_EXIST);
         * }
         */

        SourceTargetEnum sourceTarget = param.getSourceCode() == null ? PAAS : param.getSourceCode();

        PadCMDForwardDTO padCMDForwardDTO = new BlackListCMDDTO().setBlacklists(blacklists).setPadBlackMap(padBlackMap)
                .setPadAppClassifyMap(padAppClassifyMap)
                .builderForwardDTO(padCodes, sourceTarget, String.valueOf(param.getCustomerId()));
        PadTaskBO padTaskBO = padTaskComponent.addPadCMDTask(customerId, padCodes, APP_BLACK_LIST,
                padCMDForwardDTO);
        return GeneratePadTaskVO.builder(padTaskBO);
    }

    @Override
    public List<PadInstalledAppVO> listInstalledApp(PadCodesDTO param) {
        List<PadInstalledAppVO> padInstalledAppVOS = new ArrayList<>();
        param.getPadCodes().parallelStream().forEach(padCode -> {
            String key = PAD_INSTALLED_APP_PREFIX + padCode;
            Object obj = redisService.getCacheObject(key);
            if (obj != null) {
                PadInstalledAppVO padInstalledAppVO = new PadInstalledAppVO();
                padInstalledAppVO.setPadCode(padCode);
                padInstalledAppVO.setApps(JSONArray.parseArray(obj.toString(), PadInstalledAppVO.App.class));
                padInstalledAppVOS.add(padInstalledAppVO);
                return;
            }

            PadInstalledAppInformation padInstalledAppInformation = padInstalledAppInformationMapper
                    .getByPodCode(padCode);
            if (padInstalledAppInformation == null) {
                redisService.setCacheObject(key, "", 1L, TimeUnit.DAYS);
                PadInstalledAppVO padInstalledAppVO = new PadInstalledAppVO();
                padInstalledAppVO.setPadCode(padCode);
                padInstalledAppVO.setApps(Collections.emptyList());
                padInstalledAppVOS.add(padInstalledAppVO);
                return;
            }

            PadInstalledAppVO padInstalledAppVO = new PadInstalledAppVO();
            padInstalledAppVO.setPadCode(padCode);
            padInstalledAppVO
                    .setApps(JSON.parseArray(padInstalledAppInformation.getAppsJSON(), PadInstalledAppVO.App.class));
            redisService.setCacheObject(key, padInstalledAppInformation.getAppsJSON(), 1L, TimeUnit.DAYS);
            padInstalledAppVOS.add(padInstalledAppVO);
        });

        return padInstalledAppVOS;
    }

    @Override
    public Boolean gpsInjectInfo(GpsInjectInfoDTO param) {
        List<String> padCodes = param.getPadCodes();
        PadCMDForwardDTO forwardDTO = new GpsInjectInfoCMDDTO()
                .setLatitude(param.getLatitude())
                .setLongitude(param.getLongitude())
                .setAltitude(null != param.getAltitude() ? param.getAltitude() : 0)
                .builderForwardDTO(padCodes, PAAS);
        GeneratePadTaskVO.builder(
                padTaskComponent.addPadCMDTask(param.getCustomerId(), padCodes, GPS_INJECT_INFO, forwardDTO));
        return true;
    }

    @Override
    public List<GetStreamTypeVO> getStreamType(PadCodesDTO param) {
        return padMapper.listStreamTypeByPadCodes(param.getPadCodes());
    }

    @Override
    public GeneratePadTaskVO updatePadAndroidProp(UpdatePadAndroidPropDTO param) {
        // TODO VMOS 兼容问题,先注释掉,不影响下游使用
        // PadInfoVO padInfoVO = padMapper.selectPadInfoByCode(param.getPadCode());
        // //入口已验证padCode存在,这里不需要判空
        // //判断实例长连接在线(先不判断实例状态,后续任务离线失败过多,添加实例状态=10)
        // if(!Objects.equals(padInfoVO.getOnline(),1) ){
        // log.warn("PadServiceImpl.updatePadAndroidProp padCode:{} is not
        // online",param.getPadCode());
        // throw new BasicException(BasicExceptionCode.PAD_CODE_ONLINE_NOT_SUCCESS);
        // }
        Long customerId = param.getCustomerId();
        AddPadTaskDTO addTaskDTO = new AddPadTaskDTO();
        addTaskDTO.setPadCodes(new ArrayList<>(Collections.singletonList(param.getPadCode())));
        addTaskDTO.setType(UPDATE_PAD_ANDROID_PROP.getType());
        addTaskDTO.setStatus(WAIT_EXECUTE.getStatus());
        addTaskDTO.setCustomerId(customerId);
        addTaskDTO.setSourceCode(PAAS.getCode());
        addTaskDTO.setCreateBy(String.valueOf(param.getCustomerId()));

        UpdatePadAndroidPropTaskQueueBO queueBO = new UpdatePadAndroidPropTaskQueueBO();
        queueBO.setProps(param.getProps());
        queueBO.setRestart(param.getRestart());
        addTaskDTO.setTaskContent(JSON.toJSONString(queueBO));

        UpdatePadAndroidPropTaskQueueBO queueBOSimple = new UpdatePadAndroidPropTaskQueueBO();
        queueBOSimple.setRestart(param.getRestart());
        // 后面只需要这个是否重启属性 其他的不要 为节省空间 这里只保留 是否重启
        Consumer<AddPadTaskDTO> padTaskConsumer = addPadTaskDTO -> {
            addPadTaskDTO.setTaskContent(JSON.toJSONString(queueBOSimple));
        };

        PadTaskBO padTaskBO = padTaskComponent.addPadTaskWithPaas(customerId, new ArrayList<>(Arrays.asList(param.getPadCode())),
                UPDATE_PAD_ANDROID_PROP, JSON.toJSONString(queueBO), padTaskConsumer);
        return GeneratePadTaskVO.builder(padTaskBO).get(0);
    }

    @Override
    public List<GeneratePadTaskVO> replacePad(ReplacePadTaskDTO requestParam, SourceTargetEnum sourceTargetEnum) {
        log.info("PadServiceImpl.replacePad param:{} ,sourceTargetEnum:{}", JSON.toJSONString(requestParam),
                sourceTargetEnum.getCode());
        // 真机默认清除(向前兼容)
        if (Objects.isNull(requestParam.getReplacementRealAdiFlag())) {
            requestParam.setReplacementRealAdiFlag(true);
        }

        List<Pad> padList = this.getPadListByPadCode(requestParam.getPadCodes());
        if (CollectionUtils.isEmpty(padList)) {
            throw new BasicException("pad not exist");
        }
        // 默认国家码 @TODO 这个默认国家码需要后续从集群信息中获取
        // 目前集群信息中没有国家码,所以这里先写死
        final String countryCode;
        // 检测国家码
        if (null != requestParam.getCountryCode() && !requestParam.getCountryCode().isEmpty() &&
                requestParam.getCountryCode().length() == 2) {
            countryCode = requestParam.getCountryCode();
        } else {
            countryCode = "SG";
        }

        ArrayList<GeneratePadTaskVO> result = Lists.newArrayList();
        // 根据 type 字段分组
        Map<String, List<Pad>> padTypeMap = padList.stream().collect(Collectors.groupingBy(Pad::getType));
        // 虚拟机
        List<Pad> virtualPads = padTypeMap.getOrDefault(PadConstants.Type.VIRTUAL.getValue(), Lists.newArrayList());
        // 云真机
        List<Pad> realPads = padTypeMap.getOrDefault(PadConstants.Type.REAL.getValue(), Lists.newArrayList());
        // 用户选择的ADI模板
        RealPhoneTemplate userSelectedADI = null;


        if (!CollectionUtils.isEmpty(realPads)) {
            // 用户传入了adi模板
            if (Objects.nonNull(requestParam.getRealPhoneTemplateId())) {
                QueryWrapper<RealPhoneTemplate> realPhoneTemplateQueryWrapper = new QueryWrapper<>();
                realPhoneTemplateQueryWrapper.eq("delete_flag", 0);
                realPhoneTemplateQueryWrapper.eq("status", 1);
                realPhoneTemplateQueryWrapper.eq("id", requestParam.getRealPhoneTemplateId());
                userSelectedADI = realPhoneTemplateMapper.selectOne(realPhoneTemplateQueryWrapper);
                if (Objects.isNull(userSelectedADI)) {
                    throw new BasicException(REAL_PHONE_ADI_NOT_EXIST);
                }

                RealPhoneTemplate finalUserSelectedADI = userSelectedADI;
                // 判断用户选择的ADI模板是否支持当前所有的实例规格
                realPads.forEach(pad -> {
                    // 校验当前ADI模板权限问题
                    if(!redisService.isAdmin(requestParam.getCustomerId()) && finalUserSelectedADI.getIsPublic().equals(0)){
                        Long count = adiTemplateCustomerMapper.selectCount(new LambdaQueryWrapper<AdiTemplateCustomer>()
                                .eq(AdiTemplateCustomer::getCustomerId,requestParam.getCustomerId())
                                .eq(AdiTemplateCustomer::getTemplateId,requestParam.getRealPhoneTemplateId()));
                        if(count == 0){
                            throw new BasicException(REAL_PHONE_ADI_NOT_RIGHT);
                        }
                    }
                    // 如果用户选择的ADI模板不支持当前实例规格，则抛出异常
//                    if (!finalUserSelectedADI.getResourceSpecificationCode().equals(pad.getDeviceLevel())) {
//                        throw new BasicException(REAL_PHONE_ADI_NOT_SUPPORT_DEVICE_LEVEL);
//                    }
                    Integer androidVersion = customerUploadImageMapper.getAndroidVersionByImageUniqueId(pad.getImageId());
                    if (androidVersion == null) {
                        throw new BasicException(IMAGE_NOT_EXIST);
                    }
                    if (androidVersion != finalUserSelectedADI.getAndroidImageVersion()) {
                        throw new BasicException(REAL_PHONE_IMAGE_VERSION_NOT_MATCH);
                    }
                });

            }

            PadRestartTaskQueueBO padRestartTaskQueueBO = new PadRestartTaskQueueBO();
            String oprBy = String.valueOf(requestParam.getCustomerId());
            padRestartTaskQueueBO.setOprBy(oprBy);


            final RealPhoneTemplate f_userSelectedADI = userSelectedADI;

            QueryWrapper<RealPhoneTemplate> realPhoneTemplateQueryWrapper = new QueryWrapper<>();
            realPhoneTemplateQueryWrapper.eq("delete_flag", 0);
            realPhoneTemplateQueryWrapper.eq("is_public",1); // 随机ADI模板必须是公共的，客户私有的质量无法保证
            realPhoneTemplateQueryWrapper.eq("status",1); // 启用状态
            // 从数据库获取所有可用的ADI模板
            List<RealPhoneTemplate> allAvaliableADI = realPhoneTemplateMapper.selectList(realPhoneTemplateQueryWrapper);
            realPads.forEach(pad -> {
                PadUpdateAdiTaskQueueBO taskQueueBO = new PadUpdateAdiTaskQueueBO();
                JSONObject props = new JSONObject();

                AndroidDeviceInfoUtils.ramdomSimAndGPSInfo(countryCode, props);
                AndroidDeviceInfoUtils.ramdomBatteryInfo(countryCode, props);
                AndroidDeviceInfoUtils.ramdomWifiInfo(countryCode, props);
                AndroidDeviceInfoUtils.ramdomBluetoothInfo(countryCode, props);
                AndroidDeviceInfoUtils.ramdomSecurityPatch(props);


                JSONObject androidProp = requestParam.getAndroidProp();
                if (androidProp != null) {
                    props.putAll(androidProp);
                }
                taskQueueBO.setAndroidProp(JSONObject.toJSONString(props));

                RealPhoneTemplate currentADI = f_userSelectedADI;
                if (null == currentADI && CollUtil.isNotEmpty(allAvaliableADI)) {
                        // 安卓版本分组
                        Map<String, List<RealPhoneTemplate>> realAndroidMap = allAvaliableADI.stream()
                                .collect(Collectors.groupingBy(template -> template.getAndroidImageVersion() != null
                                        ? String.valueOf(template.getAndroidImageVersion())
                                        : "-1"));
                        // 根据pad的imageId获取安卓版本
                        Integer androidVersion = customerUploadImageMapper.getAndroidVersionByImageUniqueId(pad.getImageId());
                        // 获取对应安卓版本的云真机模板
                        List<RealPhoneTemplate> realPhoneTemplateList = realAndroidMap.get(androidVersion.toString());
                        // 随机获取一个模板
                        currentADI = realPhoneTemplateList.get(new Random().nextInt(realPhoneTemplateList.size()));
                }

                // 用户传入adi证书时如果没选择adi模板并且不随机时，需要从数据库中获取当前实例的adi模板
                if(currentADI == null && StringUtils.isNotBlank(requestParam.getCertificate())){
                    currentADI = realPhoneTemplateMapper.selectOne(new LambdaQueryWrapper<RealPhoneTemplate>()
                            .eq(RealPhoneTemplate::getId,pad.getRealPhoneTemplateId()));
                }

                // 挑到模板
                if (null != currentADI) {
                    taskQueueBO.setAdiUrl(currentADI.getAdiTemplateDownloadUrl());
                    taskQueueBO.setAdiPassword(currentADI.getAdiTemplatePwd());
                    taskQueueBO.setRealPhoneTemplateId(currentADI.getId());
                    taskQueueBO.setScreenLayoutCode(currentADI.getScreenLayoutCode());
                    ScreenLayout screenLayout = screenLayoutMapper
                            .selectOne(new QueryWrapper<ScreenLayout>().eq("code", currentADI.getScreenLayoutCode()));
                    // 判空,防止数据库中没有对应屏幕布局信息
                    screenLayout = Objects.isNull(screenLayout) ? new ScreenLayout() : screenLayout;
                    taskQueueBO.setScreenLayoutCode(screenLayout.getCode());
                    taskQueueBO.setLayoutWidth(screenLayout.getScreenWidth());
                    taskQueueBO.setLayoutHigh(screenLayout.getScreenHigh());
                    taskQueueBO.setLayoutDpi(screenLayout.getPixelDensity());
                    taskQueueBO.setLayoutFps(screenLayout.getScreenRefreshRate());
                    taskQueueBO.setIsReal(true);
                }
                if(StringUtils.isNotBlank(requestParam.getCertificate())){
                    taskQueueBO.setAdiCertificateRepository(requestParam.getCertificate());
                }else{

                    // 获取image_parameter字段的值（新加字段），需要在customerUploadImageMapper中实现对应方法
                    String imageParameter = customerUploadImageMapper.getImageParameterByImageUniqueId(pad.getImageId());

                    // 从数据库拿一个te证书
                    AdiCertificateRepository adiCertificateRepository = adiCertificateManager.useAdiCertificate(pad.getPadCode(), imageParameter);
                    // 证书内容
                    String certificate = adiCertificateRepository.getCertificate();
                    taskQueueBO.setAdiCertificateRepository(certificate);
                }

                // 虚拟机设置安卓属性并重置
                JSONObject finalProp = props;
                Consumer<AddPadTaskDTO> padTaskConsumer = addTaskDTO -> addTaskDTO
                        .setTaskContent(JSONObject.toJSONString(finalProp));
                PadTaskBO padTaskBO = padTaskComponent.addReplacePadTask(requestParam.getCustomerId(),
                        Collections.singletonList(pad.getPadCode()),
                        REPLACE_PAD, padTaskConsumer, JSONObject.toJSONString(taskQueueBO), sourceTargetEnum);
                result.addAll(GeneratePadTaskVO.builder(padTaskBO));
            });
        }
        if (!CollectionUtils.isEmpty(virtualPads)) {
            // 每个pad_code
            virtualPads.forEach(pad -> {
                // 根据pad的imageId获取安卓版本
                Integer androidVersion = customerUploadImageMapper.getAndroidVersionByImageUniqueId(pad.getImageId());
                // 虚拟机设置安卓属性并重置
                JSONObject props = getAndroidProp(requestParam.getCountryCode(), androidVersion.toString());

                AndroidDeviceInfoUtils.ramdomSimAndGPSInfo(countryCode, props);
                // AndroidDeviceInfoUtils.ramdomGPSInfo(countryCode, props);
                AndroidDeviceInfoUtils.ramdomBatteryInfo(countryCode, props);
                AndroidDeviceInfoUtils.ramdomWifiInfo(countryCode, props);
                AndroidDeviceInfoUtils.ramdomBluetoothInfo(countryCode, props);

                if (Objects.nonNull(requestParam.getAndroidProp())) {
                    props.putAll(requestParam.getAndroidProp());
                }
                PadUpdateAdiTaskQueueBO taskQueueBO = new PadUpdateAdiTaskQueueBO();
                taskQueueBO.setAndroidProp(JSONObject.toJSONString(props));
                Consumer<AddPadTaskDTO> padTaskConsumer = addTaskDTO -> addTaskDTO
                        .setTaskContent(JSONObject.toJSONString(props));
                PadTaskBO padTaskBO = padTaskComponent.addReplacePadTask(requestParam.getCustomerId(),
                        Collections.singletonList(pad.getPadCode()),
                        REPLACE_PAD, padTaskConsumer, JSONObject.toJSONString(taskQueueBO), sourceTargetEnum);
                result.addAll(GeneratePadTaskVO.builder(padTaskBO));
            });
        }
        return result;
    }

    @Override
    public List<GeneratePadTaskInfoVO> virtualRealSwitchUpgradeImageService(VirtualRealSwitchUpgradeImageDTO param) {
        List<String> uniquePadCodes = param.getPadCodes().stream().distinct().collect(Collectors.toList());
        param.setPadCodes(uniquePadCodes);
        long customerId = param.getCustomerId();
        LambdaQueryWrapper<CustomerUploadImage> queryWrapper = new QueryWrapper<CustomerUploadImage>().lambda();
        if (!SourceTargetEnum.ADMIN_SYSTEM.equals(param.getTaskSource())) {
            queryWrapper.and(wrapper -> wrapper.eq(CustomerUploadImage::getCustomerId, customerId).or()
                    .isNull(CustomerUploadImage::getCustomerId));
        }

        if (StringUtils.isNotEmpty(param.getImageId())) {
            queryWrapper.eq(CustomerUploadImage::getUniqueId, param.getImageId())
                    .eq(CustomerUploadImage::getStatus, TWO)
                    .eq(CustomerUploadImage::getDeleteFlag, ZERO);
            CustomerUploadImage customerUploadImage = customerUploadImageMapper.selectOne(queryWrapper);
            if (isEmpty(customerUploadImage)) {
                throw new BasicException(IMAGE_NOT_EXIST);
            }
            Pad pad = padMapper
                    .selectOne(new QueryWrapper<Pad>().lambda().eq(Pad::getPadCode, uniquePadCodes.get(ZERO)));
            if (pad.getSocModel().startsWith("QAC")) {
                param.setImageId(customerUploadImage.getImageName());
            }
        }

        // 校验当前实例是否支持真机切换
        List<Pad> padList = padMapper
                .selectList(new QueryWrapper<Pad>().in("pad_code", param.getPadCodes()).select("id", "device_level"));
        if (CollectionUtil.isEmpty(padList)) {
            throw new BasicException(PAD_CODE_NOT_EXIST);
        }

        RealPhoneTemplate realPhoneTemplate = null;
        if (param.getRealPhoneTemplateId() != null) {
            Set<String> deviceLevelList = padList.stream().map(Pad::getDeviceLevel).collect(Collectors.toSet());
            realPhoneTemplate = realPhoneTemplateMapper.selectById(param.getRealPhoneTemplateId());

            // 校验当前ADI模板权限问题
            if(!redisService.isAdmin(param.getCustomerId()) && realPhoneTemplate.getIsPublic().equals(0)){
                Long count = adiTemplateCustomerMapper.selectCount(new LambdaQueryWrapper<AdiTemplateCustomer>()
                        .eq(AdiTemplateCustomer::getCustomerId,param.getCustomerId())
                        .eq(AdiTemplateCustomer::getTemplateId,param.getRealPhoneTemplateId()));
                if(count == 0){
                    throw new BasicException(REAL_PHONE_ADI_NOT_RIGHT);
                }
            }

            if (realPhoneTemplate == null || realPhoneTemplate.getDeleteFlag()) {
                throw new BasicException(REAL_PHONE_TEMPLATE_NOT_EXIST);
            }
            if(realPhoneTemplate.getStatus() == 0){
                throw new BasicException(REAL_PHONE_ADI_UNAVAILABLE);
            }
//            for (String deviceLevel : deviceLevelList) {
//                if (!deviceLevel.equals(realPhoneTemplate.getResourceSpecificationCode())) {
//                    throw new BasicException(REAL_PHONE_TEMPLATE_NOT_EXIST);
//                }
//            }
        }

        // 云真机只能13升13 14升14  升级到虚拟机也需要校验安卓版本是否一致
//        if (PadConstants.Type.REAL.getValue().equals(param.getUpgradeImageConvertType())) {
        checkAndroidVersion(uniquePadCodes, param.getImageId(), param.getRealPhoneTemplateId(),param.getWipeData());
//        }

        PadUpgradeImageTaskQueueBO taskQueueBO = new PadUpgradeImageTaskQueueBO();
        taskQueueBO.setSourceTarget(SourceTargetEnum.PAAS.getCode());
        taskQueueBO.setImageId(param.getImageId());
        taskQueueBO.setWipeData(param.getWipeData());
        String oprBy = Optional.of(param).map(VirtualRealSwitchUpgradeImageDTO::getOprBy)
                .orElse(String.valueOf(customerId));
        taskQueueBO.setOprBy(oprBy);
        ScreenLayout screenLayout = null;
        TaskTypeConstants taskType = VIRTUAL_REAL_SWITCH_UPGRADE_IMAGE;
        if (realPhoneTemplate != null) {
            taskQueueBO.setAdiUrl(realPhoneTemplate.getAdiTemplateDownloadUrl());
            taskQueueBO.setAdiPassword(realPhoneTemplate.getAdiTemplatePwd());
            taskQueueBO.setRealPhoneTemplateId(realPhoneTemplate.getId());
            // 获取真机模板对应的布局 用于替换容器属性
            screenLayout = screenLayoutMapper
                    .selectOne(new QueryWrapper<ScreenLayout>().eq("code", realPhoneTemplate.getScreenLayoutCode()));
        } else {
            taskType = REAL_VIRTUAL_SWITCH_UPGRADE_IMAGE;
            screenLayout = screenLayoutMapper.selectById(param.getScreenLayoutId());
        }
        if (screenLayout == null || screenLayout.getDeleteFlag() == 1) {
            throw new BasicException(SCREEN_LAYOUT_NOT_EXIST);
        }
        taskQueueBO.setLayoutWidth(screenLayout.getScreenWidth());
        taskQueueBO.setLayoutHigh(screenLayout.getScreenHigh());
        taskQueueBO.setLayoutDpi(screenLayout.getPixelDensity());
        taskQueueBO.setLayoutFps(screenLayout.getScreenRefreshRate());
        // 更新pad布局
        List<Long> padIds = padList.stream().map(Pad::getId).collect(Collectors.toList());
        padMapper.updateScreenLayoutCodeByIds(padIds, screenLayout.getCode());

        Consumer<AddPadTaskDTO> padTaskConsumer = addPadTaskDTO -> {
            addPadTaskDTO.setCreateBy(oprBy);
            addPadTaskDTO.setImageId(param.getImageId());
            addPadTaskDTO.setWipeData(param.getWipeData());
        };
        PadTaskBO padTaskBO = padTaskComponent.addPadTask(customerId, param.getPadCodes(),
                taskType, padTaskConsumer, JSON.toJSONString(taskQueueBO), PAAS);
        return GeneratePadTaskInfoVO.builder(padTaskBO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<NetStorageCreateVO> virtualizeNetStorageRes(NetWorkVirtualizeDTO param, SourceTargetEnum sourceCode) {

        if (Objects.nonNull(param.getStorageSize())) {
            if (checkStorageCapacity(param.getStorageSize())) {
                throw new BasicException(PadExceptionCode.NETWORK_INSTANCE_STORAGE_CAPACITY_NOT_EXIST_REQUIRED_MESSAGE);
            }
        }

        param.setSourceTarget(sourceCode);
        //实例规格
        ResourceSpecification resourceSpecification = resourceSpecificationMapper.selectOne(new QueryWrapper<ResourceSpecification>().lambda()
                .eq(ResourceSpecification::getSpecificationCode, param.getSpecificationCode())
                .eq(ResourceSpecification::getDeleteFlag, ZERO)
                .eq(ResourceSpecification::getStatus, ONE)
                .last("LIMIT 1"));
        if (BeanUtil.isEmpty(resourceSpecification)) {
            throw new BasicException(SPECIFICATION_CODE_NOT_EXIST);
        }
        //实例数量
//        Integer number = param.getNumber();
//        //网络存储容量不足 这里需要加redis锁
//        if (!netStorageResService.canDeductSize(param.getCustomerId(),param.getClusterCode(), (long) number * param.getStorageSize())) {
//            throw new BasicException(PadExceptionCode.NET_STORAGE_INSUFFICIENT_CAPACITY);
//        }
        LambdaQueryWrapper<NetStorageRes> resWrapper = new LambdaQueryWrapper<>();
        resWrapper.eq(NetStorageRes::getClusterCode, param.getClusterCode()).eq(NetStorageRes::getCustomerId,param.getCustomerId()).last("LIMIT 1");
        NetStorageRes storageRes = netStorageResService.getOne(resWrapper);
        if(Objects.isNull(storageRes) || storageRes.getStorageCapacity()<= 0) {
            throw new BasicException(PadExceptionCode.NET_STORAGE_INSUFFICIENT_CAPACITY);
        }
        //镜像
        LambdaQueryWrapper<CustomerUploadImage> imageQueryWrapper = new QueryWrapper<CustomerUploadImage>().lambda();
        if (!SourceTargetEnum.ADMIN_SYSTEM.getCode().equals(sourceCode.getCode())) {
            imageQueryWrapper.and(wrapper -> wrapper.eq(CustomerUploadImage::getCustomerId, param.getCustomerId()).or().isNull(CustomerUploadImage::getCustomerId));
        }
        imageQueryWrapper.eq(CustomerUploadImage::getUniqueId, param.getImageId())
                .eq(CustomerUploadImage::getDeleteFlag, ZERO)
                .eq(CustomerUploadImage::getStatus, ImageUploadStatus.SUCCESS.getStatus())
                .last("LIMIT 1");
        CustomerUploadImage customerUploadImage = customerUploadImageMapper.selectOne(imageQueryWrapper);
        if (BeanUtil.isEmpty(customerUploadImage)) {
            throw new BasicException(IMAGE_NOT_EXIST);
        }
        RealPhoneTemplate realPhoneTemplate = null;
        if(param.getRandomADITemplates()){
            LambdaQueryWrapper<RealPhoneTemplate> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(RealPhoneTemplate::getDeleteFlag, "0")
                    .eq(RealPhoneTemplate::getAndroidImageVersion, customerUploadImage.getAndroidImageVersion())
//                    .eq(RealPhoneTemplate::getResourceSpecificationCode, param.getSpecificationCode())
                    .eq(RealPhoneTemplate::getIsPublic,1) // 随机ADI模板必须是公共的，客户私有的质量无法保证
                    .eq(RealPhoneTemplate::getStatus,1) // 启用状态
                    .last("ORDER BY RAND() LIMIT 1"); // 添加随机排序并限制1条
            realPhoneTemplate = realPhoneTemplateMapper.selectOne(wrapper);
            if(Objects.isNull(realPhoneTemplate)){
                throw new BasicException(REAL_PHONE_ADI_NOT_EXIST);
            }
            param.setRealPhoneTemplateId(realPhoneTemplate.getId());
        }
        //验证adi模板
        else if (Objects.nonNull(param.getRealPhoneTemplateId())) {
            QueryWrapper<RealPhoneTemplate> realPhoneTemplateQueryWrapper = new QueryWrapper<>();
            realPhoneTemplateQueryWrapper.eq("delete_flag", 0);
            realPhoneTemplateQueryWrapper.eq("id", param.getRealPhoneTemplateId());
            realPhoneTemplate = realPhoneTemplateMapper.selectOne(realPhoneTemplateQueryWrapper);
            if (Objects.isNull(realPhoneTemplate)) {
                throw new BasicException(REAL_PHONE_ADI_NOT_EXIST);
            }
        }
        //屏幕布局
        String layoutCode = param.getScreenLayoutCode();
        if(Objects.nonNull(param.getRealPhoneTemplateId())){
            layoutCode =realPhoneTemplate.getScreenLayoutCode();
        }
        LambdaQueryWrapper<ScreenLayout> screenLayoutQueryWrapper = new QueryWrapper<ScreenLayout>().lambda();
        if (!SourceTargetEnum.ADMIN_SYSTEM.getCode().equals(sourceCode.getCode())) {
            screenLayoutQueryWrapper.and(wrapper -> wrapper.eq(ScreenLayout::getCustomerId, param.getCustomerId()).or().isNull(ScreenLayout::getCustomerId));
        }
        screenLayoutQueryWrapper.eq(ScreenLayout::getCode, layoutCode)
                .eq(ScreenLayout::getDeleteFlag, ZERO)
                .eq(ScreenLayout::getStatus, ONE)
                .last("LIMIT 1");
        ScreenLayout screenLayout = screenLayoutMapper.selectOne(screenLayoutQueryWrapper);
        if (BeanUtil.isEmpty(screenLayout)) {
            throw new BasicException(SCREEN_LAYOUT_NOT_EXIST);
        }

        Long customerId = param.getCustomerId();
        if (isNotEmpty(customerId)) {
            Integer streamType = customerConfigMapper.getStreamTypeByCustomerId(customerId);
            param.setStreamType(streamType);
        }
        List<Pad> padList = Lists.newArrayList();
        padList.addAll(virtualizeDeviceNewNet(param, resourceSpecification, screenLayout));
        //扣除网络存储资源使用量
        Boolean aBoolean = netStorageResService.deductsTheSizeOfTheResourcesUsed(param.getCustomerId(),param.getClusterCode(), padList);
        return padList.stream().map(NetStorageCreateVO::build).collect(Collectors.toList());
    }


    @Override
    public List<GeneratePadTaskVO> simulateTouch(SimulateTouchDTO param) {
        SimulateTouchCMDDTO simulateTouchCMDDTO = new SimulateTouchCMDDTO();
        simulateTouchCMDDTO.setSimulateHeight(param.getHeight());
        simulateTouchCMDDTO.setSimulateWidth(param.getWidth());
        List<SimulateTouchCMDDTO.Position> positions = new ArrayList<>();
        for(SimulateTouchDTO.Position position : param.getPositions()){
            SimulateTouchCMDDTO.Position position1 = new SimulateTouchCMDDTO.Position();
            position1.setActionType(position.getActionType());
            position1.setX(position.getX());
            position1.setY(position.getY());
            position1.setNextPositionWaitTime(position.getNextPositionWaitTime());
            position1.setSwipe(position.getSwipe());
            position1.setKeyCode(position.getKeyCode());
            position1.setTouchType(position.getTouchType());
            positions.add(position1);
        }
        simulateTouchCMDDTO.setSimulateList(positions);
        PadCMDForwardDTO padCMDForwardDTO = simulateTouchCMDDTO.builderForwardDTO(param.getPadCodes(), PAAS);
        PadTaskBO padTaskBO = padTaskComponent.addPadCMDTask(param.getCustomerId(), param.getPadCodes(),
                SIMULATE_TOUCH, padCMDForwardDTO);
        return GeneratePadTaskVO.builder(padTaskBO);
    }

    @Override
    public List<GeneratePadTaskVO> inputText(InputTextDTO param) {
        InputTextCMDDTO inputTextCMDDTO = new InputTextCMDDTO(param.getText());
        PadCMDForwardDTO padCMDForwardDTO = inputTextCMDDTO.builderForwardDTO(param.getPadCodes(), PAAS);
        PadTaskBO padTaskBO = padTaskComponent.addPadCMDTask(param.getCustomerId(), param.getPadCodes(),
                SET_COMMIT_TEXT, padCMDForwardDTO);
        return GeneratePadTaskVO.builder(padTaskBO);
    }

    @Override
    public List<GeneratePadTaskVO> addPhoneRecord(CallRecordsDTO param) {
        PadCallRecordsCMDDTO padCallRecordsCMDDTO = new PadCallRecordsCMDDTO();
        List<PadCallRecordsCMDDTO.PhoneRecord> phoneRecords = new ArrayList<>();
        for(CallRecordsDTO.CallRecord record : param.getCallRecords()) {
            PadCallRecordsCMDDTO.PhoneRecord phoneRecord = new PadCallRecordsCMDDTO.PhoneRecord();
            phoneRecord.setNumber(record.getNumber());
            phoneRecord.setInputType(record.getInputType());
            phoneRecord.setDuration(record.getDuration());
            phoneRecord.setTimeString(record.getTimeString());
            phoneRecords.add(phoneRecord);
        }

        padCallRecordsCMDDTO.setPhoneRecordList(phoneRecords);
        PadCMDForwardDTO padCMDForwardDTO = padCallRecordsCMDDTO.builderForwardDTO(param.getPadCodes(), PAAS);
        PadTaskBO padTaskBO = padTaskComponent.addPadCMDTask(param.getCustomerId(), param.getPadCodes(),
                ADD_PHONE_RECORD, padCMDForwardDTO);
        return GeneratePadTaskVO.builder(padTaskBO);
    }

    @Override
    public void updatePadRealPhoneTemplate(String padCode, Long realPhoneTemplateId) {
        padMapper.update(new UpdateWrapper<Pad>().set("real_phone_template_id", realPhoneTemplateId).eq("pad_code", padCode));
    }

    @Override
    public List<GeneratePadTaskVO> resetGAID(ResetGaidDTO param) {
        log.info("重置 GAID, param: {}", JSON.toJSONString(param));

        String oprBy = param.getOprBy();
        List<String> padCodes = param.getPadCodes();
        String resetGmsType = param.getResetGmsType();

        if (CollUtil.isEmpty(param.getPadCodes()) && CollUtil.isEmpty(param.getGroupIds())) {
            throw new BasicException(PARAMETER_EXCEPTION);
        }
        long customerId = param.getCustomerId();
        if (redisService.isAdmin(customerId)) {
            customerId = 0L;
        }

        List<Pad> padList = padMapper.getPadByGroupIds(param.getPadCodes(), param.getGroupIds(), customerId);
        if (CollectionUtils.isEmpty(padList)) {
            throw new BasicException(PAD_CODE_NOT_EXIST);
        }

        // 构建任务下发参数
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        Map<String, Object> dataParam = new HashMap<>();
        dataParam.put("resetGmsType", resetGmsType);
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(dataParam).setPadCode(padCode)));

        PadCMDForwardDTO dto = new PadCMDForwardDTO();
        dto.setCommand(net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.RESET_GAID);
        dto.setSourceCode(param.getTaskSource());
        dto.setPadInfos(padInfos);

        PadTaskBO padTaskBO = padTaskComponent.addPadCMDTask(customerId, padCodes, RESET_GAID, dto);

        log.info("任务提交结果 padTaskBO: {}", JSON.toJSONString(padTaskBO));
        return GeneratePadTaskVO.builder(padTaskBO);
    }

    @Override
    public List<GeneratePadTaskVO> netStorageResBootOn(NetWorkOnDTO param, SourceTargetEnum sourceTargetEnum) {
        long beginTime = System.currentTimeMillis();
        PadDetailsDTO dto = new PadDetailsDTO();
        dto.setPadCodes(Lists.newArrayList(param.getPadCodes()));
        List<PadDetailsVO> detailsVOList = padMapper.selectDetailsByPadCode(dto);
        //过滤掉已经开机的实例
        detailsVOList.removeIf(padDetailsVO -> {
            padDetailsVO.setDataSize(padDetailsVO.getNetStorageResSize());
            return !Objects.equals(padDetailsVO.getPadStatus(), PadStatusConstant.OFF) && !Objects.equals(padDetailsVO.getPadStatus(), PadStatusConstant.ON_ERROR);
        });
        //没有可以开机的实例
        if (CollectionUtils.isEmpty(detailsVOList)) {
            throw new BasicException(POD_OFFLINE_ONLINE);
        }

        List<Long> customerIds = detailsVOList.stream().map(PadDetailsVO::getCustomerId).distinct().collect(Collectors.toList());
        //一次只允许操作一个用户下的网存实例,否则抛出异常. 这里涉及到扣减算力以及数据匹配,多个用户不便于上锁,先做简单处理.
        if(customerIds.size()>1){
            throw new BasicException(PadExceptionCode.NETWORK_RES_DISABLE_THE_ACTION);
        }

        // 检查是否有正在执行的同步备份任务
        for (String padCode : dto.getPadCodes()) {
            // 查询是否有执行中的备份任务（直接查询Task和PadTask表）
            boolean hasExecutingBackupTask = hasExecutingBackupTask(padCode);
            if (hasExecutingBackupTask) {
                throw new BasicException(NET_SYNC_DATA_EXECUTING);
            }
        }

        // 取消待执行的同步备份任务
        for (String padCode : dto.getPadCodes()) {
            // 查找并取消待执行的同步备份任务
            cancelPendingBackupTasks(padCode);
        }

        // 区分关机失败的实例和需要重新匹配算力的实例
        List<String> allPadCodes = detailsVOList.stream().map(PadDetailsVO::getPadCode).collect(Collectors.toList());
        Map<String, PadDetailsVO> padDetailsMap = detailsVOList.stream()
                .collect(Collectors.toMap(PadDetailsVO::getPadCode, vo -> vo, (v1, v2) -> v1));

        // 识别关机失败的实例（具有NET_STORAGE_OFF类型的FAIL_ALL状态任务）
        Map<String, NetStorageComputeUnit> failedShutdownResourceMap = new HashMap<>();
        List<String> failedShutdownPadCodes = new ArrayList<>();
        List<String> normalPadCodes = new ArrayList<>();

        for (String padCode : allPadCodes) {
            // 检查实例是否有同步失败任务的状态（状态码为-1：FAIL_ALL）
            PadTask padTask = padTaskMapper.getLatestPadTaskByType(TaskTypeConstants.NET_SYNC_BACKUP.getType(),padCode);
            // 只有在有关机失败任务的情况下，才去查询绑定的算力资源
            if (Objects.nonNull(padTask) && (padTask.getStatus().equals(TaskStatusConstants.FAIL_ALL.getStatus())
                    || padTask.getStatus().equals(TaskStatusConstants.FAIL_PART.getStatus()) )) {
                log.info("Found failed shutdown task for pad: {}", padCode);

                // 查询该实例是否有绑定的且状态为已占用的算力单元
                NetStorageComputeUnit boundComputeUnit = netStorageComputeUnitService.getBoundComputeUnitByPadCode(padCode);
                if (boundComputeUnit != null && boundComputeUnit.getBindFlag() == 1) {
                    log.info("Found bound compute unit for pad with failed shutdown: {}, compute unit: {}",
                            padCode, boundComputeUnit.getNetStorageComputeUnitCode());
                    failedShutdownResourceMap.put(padCode, boundComputeUnit);
                    failedShutdownPadCodes.add(padCode);
                } else {
                    // 如果找不到绑定的算力单元，则作为正常实例处理
                    normalPadCodes.add(padCode);
                }
            } else {
                // 没有关机失败任务，作为正常实例处理
                normalPadCodes.add(padCode);
            }
        }



//        String lockKey = LockKeyConstants.NetStorageProcess.NET_STORAGE_COMPUTE_LOCK_KEY_PREFIX + customerIds.get(0);
//        RLock rLock = redissonDistributedLock.tryLock(lockKey, 5, 60);
//        if(Objects.isNull(rLock)){
//            throw new BasicException(PadExceptionCode.NETWORK_RES_FREQUENT_OPERATION);
//        }
        String countryCode = param.getCountryCode();
        List<PadTaskBO> padTaskBOList = Lists.newArrayList();

//        try {
            // 1. 处理正常需要重新匹配算力的实例
            if (!normalPadCodes.isEmpty()) {
                List<PadDetailsVO> normalPadDetails = normalPadCodes.stream()
                        .map(padDetailsMap::get)
                        .collect(Collectors.toList());

                Map<String, List<PadDetailsVO>> clusterCodeMap = normalPadDetails.stream()
                        .collect(Collectors.groupingBy(PadDetailsVO::getClusterCode));

                List<NetStorageDTO> normalNetStorageDTOlist = Lists.newArrayList();
                //匹配算力
                clusterCodeMap.forEach((clusterCode, padDetailsVOList) ->
                        normalNetStorageDTOlist.addAll(netStoragePadHelper.NetStorageMatchComputeAndStorage(
                                clusterCode, padDetailsVOList, countryCode, param.getAndroidProp()))
                );
                //筛选算力
                List<NetStorageComputeUnit> allComputeUnitList = normalNetStorageDTOlist.stream()
                        .filter(bean -> bean.getNetStorageComputeUnitList() != null) // 过滤掉 netStorageComputeUnitList 为空的项
                        .flatMap(bean -> bean.getNetStorageComputeUnitList().stream()) // 将每个 netStorageComputeUnitList 转成流并合并
                        .collect(Collectors.toList());

                if (!allComputeUnitList.isEmpty()) {
                    boolean successBound = false;

                    List<String> normalPadCodeList = normalNetStorageDTOlist.stream()
                            .filter(bean -> bean.getPadDetailsVOList() != null)
                            .flatMap(bean -> bean.getPadDetailsVOList().stream())
                            .map(PadDetailsVO::getPadCode)
                            .collect(Collectors.toList());
                    if (!normalPadCodeList.isEmpty()) {
                        // 为正常实例创建任务
                        PadTaskBO normalPadTaskBO = padTaskComponent.addReplacePadTask(
                                param.getCustomerId(),
                                normalPadCodeList,
                                CONTAINER_NET_STORAGE_ON,
                                null,
                                JSON.toJSONString(normalNetStorageDTOlist),
                                sourceTargetEnum
                        );
                        if (normalPadTaskBO != null) {
                            padTaskBOList.add(normalPadTaskBO);

                            // 处理正常实例的虚拟化设置
                            processNormalPadInstances(normalNetStorageDTOlist, normalPadTaskBO);

                            // 保存正常实例的算力、网存映射关系
                            netStoragePadHelper.saveNetStorageDTOList(normalNetStorageDTOlist);
                            successBound = true;
                        }
                    }
                    //如果未成功绑定,设置成未绑定状态
                    if (!successBound) {
                        //所有未使用的网存code
                        List<String> netStorageResCodeList = normalNetStorageDTOlist.stream().flatMap(bean -> bean.getNetStorageComputeUnitPadList().stream().map(NetStorageComputeUnitPad::getNetStorageResCode)).collect(Collectors.toList());
                        //更新网存实例为未绑定
                        netStorageResUnitService.updateBatchBindByCodeList(netStorageResCodeList,0);
                        log.info("netStorageResBootOn 未绑定算力成功,释放算力 netStorageResCodeList:{}", netStorageResCodeList);
                    }
                }
            }
            // 2. 处理关机失败的实例（单独创建任务，不创建新的关系）
            if (!failedShutdownPadCodes.isEmpty()) {
                log.info("Processing failed shutdown instances: {}", failedShutdownPadCodes);
                try {
                    List<NetStorageDTO> failedNetStorageDTOlist = processFailedShutdownInstances(failedShutdownPadCodes, failedShutdownResourceMap, padDetailsMap,param);
                    // 为关机失败的实例创建单独的任务
                    PadTaskBO failedShutdownTaskBO = padTaskComponent.addReplacePadTask(
                            param.getCustomerId(),
                            failedShutdownPadCodes,
                            CONTAINER_NET_STORAGE_ON,
                            null,
                            JSON.toJSONString(failedNetStorageDTOlist),
                            sourceTargetEnum
                    );

                    if (failedShutdownTaskBO != null) {
                        padTaskBOList.add(failedShutdownTaskBO);
                        List<String> netStorageResCodeList = failedNetStorageDTOlist.stream().flatMap(bean -> bean.getNetStorageComputeUnitPadList().stream().map(NetStorageComputeUnitPad::getNetStorageResCode)).collect(Collectors.toList());
                        netStorageResUnitService.updateBatchBindByCodeList(netStorageResCodeList,1);
                    }
                }catch (Exception e){
                    log.error("create failedShutdownTaskBO error",e);
                }

            }
            // 如果没有成功匹配任何算力（正常实例和关机失败实例都没有）
            if (padTaskBOList.isEmpty()) {
                throw new BasicException(PadExceptionCode.DEVICE_NUMBER_INSUFFICIENT_QUANTITY);
            }

            return padTaskBOList.stream()
                    .map(GeneratePadTaskVO::builder)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
//        }
//        finally {
//            if (rLock.isHeldByCurrentThread()) {
//                rLock.unlock();
//            } else {
//                log.warn("Thread [{}] attempted to unlock key [{}], but the current thread does not hold the lock. Potential lock contention issue!",
//                        Thread.currentThread().getName(), lockKey);
//            }
//        }
    }

    /**
     * 处理正常实例的虚拟化设置
     *
     * @param netStorageDTOlist 匹配的网存DTO列表
     * @param padTaskBO 任务对象
     */
    private void processNormalPadInstances(List<NetStorageDTO> netStorageDTOlist, PadTaskBO padTaskBO) {
        Map<String, PadTaskBO.PadSubTaskBO> subTaskMap = Optional.ofNullable(padTaskBO.getSubTasks())
                .orElse(Collections.emptyList())
                .stream()
                .filter(subTask -> subTask.getPadCode() != null)
                .collect(Collectors.toMap(PadTaskBO.PadSubTaskBO::getPadCode, subTask -> subTask, (oldValue, newValue) -> newValue));

        for (NetStorageDTO netStorageDTO : netStorageDTOlist) {
            netStorageDTO.getNetStoragePadCodeDetailDTOList().forEach(netStoragePadCodeDetailDTO -> {
                String padCode = netStoragePadCodeDetailDTO.getPadCode();
                VirtualizeDeviceInfoVO virtualizeDeviceInfoVOS = null;
                try {
                    //获取规格信息
                    ResourceSpecification resourceSpecification = netStoragePadHelper.getResourceSpecification(netStorageDTO.getDeviceLevel());
                    //添加cache
                    virtualizeDeviceInfoVOS = netStoragePadHelper.getVirtualizeDevice(netStoragePadCodeDetailDTO.getNetStorageComputeUnit().getDeviceId());
                    virtualizeDeviceNewNetByPadDetail(virtualizeDeviceInfoVOS, resourceSpecification, netStoragePadCodeDetailDTO.getPadDetailsVO(), subTaskMap);
                } catch (Exception e) {
                    //回写CMS云机任务结果
                    PadTaskBO.PadSubTaskBO padSubTaskBO = subTaskMap.get(padCode);
                    PadTask padTask = new PadTask();
                    padTask.setId(padSubTaskBO.getSubTaskId());
                    padTask.setStatus(TaskStatusConstants.FAIL_ALL.getStatus());
                    padTask.setErrorMsg("云机开机失败");
                    padTask.setEndTime(new Date());
                    //任务置为失败
                    padTaskMapper.updateById(padTask);
                    UpdateSubTaskDTO taskDTO = new UpdateSubTaskDTO();
                    taskDTO.setMasterTaskId(padSubTaskBO.getMasterTaskId());
                    taskDTO.setSubTaskId(padSubTaskBO.getSubTaskId());
                    taskDTO.setSubTaskStatus(TaskStatusConstants.FAIL_ALL.getStatus());
                    taskService.updateSubTaskStatus(taskDTO);
                    //失败的IP进入过滤池
                    if (netStoragePadCodeDetailDTO.getPadDetailsVO() != null && StrUtil.isNotBlank(netStoragePadCodeDetailDTO.getPadDetailsVO().getPadIp())) {
                        log.info("云机开机失败 增加IP到过滤池 padCode:{},padIp:{}", padCode, netStoragePadCodeDetailDTO.getPadDetailsVO().getPadIp());
                        String ipPrefix = IpUtil.getIpPrefix(netStoragePadCodeDetailDTO.getPadDetailsVO().getPadIp());
                        String redisKey = CBS_INSTANCE_IP + ipPrefix;
                        redisService.setCacheSetValue(redisKey, netStoragePadCodeDetailDTO.getPadDetailsVO().getPadIp());
                    }

                    // 更新网存pad的id,如果不更新ip会导致ip冲突,多个实例占用同一个ip
                    updatePadIpOnStartupFailure(padCode, netStoragePadCodeDetailDTO, virtualizeDeviceInfoVOS);

                    log.error(">>>>>>>>>>>>>>>>云机开机失败,device={}", JSON.toJSONString(netStoragePadCodeDetailDTO), e);
                } finally {
                    List<String> padIps = padMapper.getPadIpsByDeviceId(netStoragePadCodeDetailDTO.getNetStorageComputeUnit().getDeviceId());
                    for (String padIp : padIps) {
                        String key = RedisKeyPrefix.PAD_IP_LOCK + padIp;
                        redisService.deleteObject(key);
                    }
                }
            });
        }
    }

    /**
     * 云机开机失败时更新IP信息，防止IP冲突
     *
     * @param padCode 实例编码
     * @param netStoragePadCodeDetailDTO 网存实例详情对象
     * @param virtualizeDeviceInfoVOS 虚拟化设备信息
     */
    private void updatePadIpOnStartupFailure(String padCode, NetStoragePadCodeDetailDTO netStoragePadCodeDetailDTO,
                                            VirtualizeDeviceInfoVO virtualizeDeviceInfoVOS) {
        if (padCode == null || netStoragePadCodeDetailDTO == null ||
            netStoragePadCodeDetailDTO.getPadDetailsVO() == null ||
            netStoragePadCodeDetailDTO.getPadDetailsVO().getPadIp() == null) {
            log.warn("更新云机IP失败，参数不完整");
            return;
        }

        Pad pad = new Pad();
        pad.setPadCode(padCode);
        pad.setPadIp(netStoragePadCodeDetailDTO.getPadDetailsVO().getPadIp());

        if (StringUtils.isNotBlank(netStoragePadCodeDetailDTO.getPadDetailsVO().getNetStorageResId())) {
            pad.setNetStorageResId(netStoragePadCodeDetailDTO.getPadDetailsVO().getNetStorageResId());
        }

        if (virtualizeDeviceInfoVOS != null && StringUtils.isNotBlank(virtualizeDeviceInfoVOS.getArmServerCode())) {
            pad.setArmServerCode(virtualizeDeviceInfoVOS.getArmServerCode());
        }

        padMapper.batchUpdatePadIp(Collections.singletonList(pad));
    }

    /**
     * 处理关机失败的实例
     *
     * @param failedShutdownPadCodes 关机失败的实例编码列表
     * @param failedShutdownResourceMap 实例编码到算力单元的映射
     * @param padDetailsMap 实例编码到详情对象的映射
     */
    private List<NetStorageDTO> processFailedShutdownInstances(List<String> failedShutdownPadCodes,
                                                Map<String, NetStorageComputeUnit> failedShutdownResourceMap,
                                                Map<String, PadDetailsVO> padDetailsMap,NetWorkOnDTO param) {
        // 按集群分组实例
        Map<String, List<String>> clusterPadCodesMap = new HashMap<>();
        for (String padCode : failedShutdownPadCodes) {
            PadDetailsVO padDetailsVO = padDetailsMap.get(padCode);
            if (padDetailsVO != null) {
                String clusterCode = padDetailsVO.getClusterCode();
                clusterPadCodesMap.computeIfAbsent(clusterCode, k -> new ArrayList<>()).add(padCode);
            }
        }
        List<NetStorageDTO> netStorageDTOS = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : clusterPadCodesMap.entrySet()) {
            String clusterCode = entry.getKey();
            List<String> clusterPadCodes = entry.getValue();
            log.info("Processing failed shutdown instances for cluster: {}, instances: {}", clusterCode, clusterPadCodes);

            // 按设备规格分组
            Map<String, List<PadDetailsVO>> deviceLevelMap = new HashMap<>();
            for (String padCode : clusterPadCodes) {
                PadDetailsVO padDetailsVO = padDetailsMap.get(padCode);
                if (padDetailsVO != null) {
                    String deviceLevel = padDetailsVO.getDeviceLevel();
                    deviceLevelMap.computeIfAbsent(deviceLevel, k -> new ArrayList<>()).add(padDetailsVO);
                }
            }
            // 为每个设备规格创建一个NetStorageDTO
            netStorageDTOS.addAll(netStorageComputeUnitService.getPadComputeUnitExists(deviceLevelMap,clusterCode,failedShutdownResourceMap,param));
        }
        return netStorageDTOS;
    }

    @Override
    public GeneratePadTaskVO netStorageResSpecifiedCodeBootOn(NetStorageNetWorkOffDTO param, SourceTargetEnum sourceTargetEnum) {
        checkNetStorageResUnitCode(param);
        PadDetailsDTO dto = new PadDetailsDTO();
        dto.setPadCodes(Lists.newArrayList(param.getPadCode()));
        List<PadDetailsVO> detailsVOList = padMapper.selectDetailsByPadCode(dto);
        //过滤掉已经开机的实例
        detailsVOList.removeIf(padDetailsVO -> {
            padDetailsVO.setDataSize(padDetailsVO.getNetStorageResSize());
            padDetailsVO.setTargetStorageResId(param.getNetStorageResUnitCode());
            //标记为指定网存开机
            padDetailsVO.setUpdateNetStorageResFlag(true);
            log.info("开机实例列表:{},getNetStorageResUnitCode :{}", JSON.toJSONString(padDetailsVO),param.getNetStorageResUnitCode());
            return !Arrays.asList(PadStatusConstant.OFF).contains(padDetailsVO.getPadStatus());

        });
        //没有可以开机的实例
        if (CollectionUtils.isEmpty(detailsVOList)) {
            throw new BasicException(POD_OFFLINE_ONLINE);
        }
//        String lockKey = LockKeyConstants.NetStorageProcess.NET_STORAGE_COMPUTE_LOCK_KEY_PREFIX + detailsVOList.get(0).getCustomerId();
//        RLock rLock = redissonDistributedLock.tryLock(lockKey, 5, 60);
//        if(Objects.isNull(rLock)){
//            throw new BasicException(PadExceptionCode.NETWORK_RES_FREQUENT_OPERATION);
//        }

        // 检查是否有正在执行的同步备份任务
        boolean hasExecutingBackupTask = hasExecutingBackupTask(param.getPadCode());
        if (hasExecutingBackupTask) {
            throw new BasicException(NET_SYNC_DATA_EXECUTING);
        }else{
            cancelPendingBackupTasks(param.getPadCode());
        }

        // 检查实例是否有同步失败任务的状态（状态码为-1：FAIL_ALL）
        PadTask padTaskLastly = padTaskMapper.getLatestPadTaskByType(TaskTypeConstants.NET_SYNC_BACKUP.getType(),param.getPadCode());
        // 只有在有关机失败任务的情况下，才去查询绑定的算力资源
        List<PadTaskBO> padTaskBOList = Lists.newArrayList();
        Map<String, NetStorageComputeUnit> failedShutdownResourceMap = new HashMap<>();
        List<String> failedShutdownPadCodes = new ArrayList<>();
        if (Objects.nonNull(padTaskLastly) && (padTaskLastly.getStatus().equals(TaskStatusConstants.FAIL_ALL.getStatus())
                || padTaskLastly.getStatus().equals(TaskStatusConstants.FAIL_PART.getStatus()) )) {
            log.info("netStorageResSpecifiedCodeBootOn Found failed shutdown task for pad: {}",  param.getPadCode());
            // 查询该实例是否有绑定的且状态为已占用的算力单元
            NetStorageComputeUnit boundComputeUnit = netStorageComputeUnitService.getBoundComputeUnitByPadCode(param.getPadCode());
            if (boundComputeUnit != null && boundComputeUnit.getBindFlag() == 1) {
                log.info("netStorageResSpecifiedCodeBootOn Found bound compute unit for pad with failed shutdown: {}, compute unit: {}",
                        param.getPadCode(), boundComputeUnit.getNetStorageComputeUnitCode());
                failedShutdownResourceMap.put(param.getPadCode(), boundComputeUnit);
                failedShutdownPadCodes.add(param.getPadCode());
            }
        }
        if(CollUtil.isNotEmpty(failedShutdownPadCodes)){
            try {
                Map<String, PadDetailsVO> padDetailsMap = detailsVOList.stream()
                        .collect(Collectors.toMap(PadDetailsVO::getPadCode, vo -> vo, (v1, v2) -> v1));
                NetWorkOnDTO netWorkOnDTO = new NetWorkOnDTO();
                netWorkOnDTO.setCountryCode(param.getCountryCode());
                netWorkOnDTO.setAndroidProp(param.getAndroidProp());
                netWorkOnDTO.setClusterCode(param.getClusterCode());
                netWorkOnDTO.setPadCodes(Collections.singletonList(param.getPadCode()));
                List<NetStorageDTO> failedNetStorageDTOlist = processFailedShutdownInstances(failedShutdownPadCodes, failedShutdownResourceMap, padDetailsMap,netWorkOnDTO);
                // 为关机失败的实例创建单独的任务
                PadTaskBO failedShutdownTaskBO = padTaskComponent.addReplacePadTask(
                        param.getCustomerId(),
                        Collections.singletonList(param.getPadCode()),
                        CONTAINER_NET_STORAGE_ON,
                        null,
                        JSON.toJSONString(failedNetStorageDTOlist),
                        sourceTargetEnum
                );

                if (failedShutdownTaskBO != null) {
                    padTaskBOList.add(failedShutdownTaskBO);
                    List<String> netStorageResCodeList = failedNetStorageDTOlist.stream().flatMap(bean -> bean.getNetStorageComputeUnitPadList().stream().map(NetStorageComputeUnitPad::getNetStorageResCode)).collect(Collectors.toList());
                    netStorageResUnitService.updateBatchBindByCodeList(netStorageResCodeList,1);
                }
            }catch (Exception e){
                log.error("create failedShutdownTaskBO error",e);
            }
        }else{
            List<NetStorageDTO> netStorageDTOlist = Lists.newArrayList();
            Map<String, List<PadDetailsVO>> clusterCodeMap = detailsVOList.stream()
                    .collect(Collectors.groupingBy(PadDetailsVO::getClusterCode));
            //匹配算力
            clusterCodeMap.forEach((clusterCode, padDetailsVOList) -> netStorageDTOlist.addAll(netStoragePadHelper.NetStorageMatchComputeAndStorage(clusterCode, padDetailsVOList,param.getCountryCode(),param.getAndroidProp())));
            //筛选算力
            List<NetStorageComputeUnit> allComputeUnitList = netStorageDTOlist.stream()
                    .filter(bean -> bean.getNetStorageComputeUnitList() != null) // 过滤掉 netStorageComputeUnitList 为空的项
                    .flatMap(bean -> bean.getNetStorageComputeUnitList().stream()) // 将每个 netStorageComputeUnitList 转成流并合并
                    .collect(Collectors.toList());
            // 匹配不到算力
            if (CollectionUtils.isEmpty(allComputeUnitList)) {
                throw new BasicException(PadExceptionCode.DEVICE_NUMBER_INSUFFICIENT_QUANTITY);
            }
            List<String> padCodeList = netStorageDTOlist.stream()
                    .filter(bean -> bean.getPadDetailsVOList() != null) // 过滤掉 netStorageComputeUnitList 为空的项
                    .flatMap(bean -> bean.getPadDetailsVOList().stream())
                    .map(PadDetailsVO::getPadCode)// 将每个 netStorageComputeUnitList 转成流并合并
                    .collect(Collectors.toList());

            padTaskBOList.add(padTaskComponent.addReplacePadTask(param.getCustomerId(), padCodeList, CONTAINER_NET_STORAGE_ON, null, JSON.toJSONString(netStorageDTOlist), sourceTargetEnum));
            Map<String, PadTaskBO.PadSubTaskBO> subTaskMap = padTaskBOList.stream()
                    .filter(Objects::nonNull)
                    .flatMap(padTask -> Optional.ofNullable(padTask.getSubTasks()).orElse(Collections.emptyList()).stream())
                    .filter(subTask -> subTask.getPadCode() != null) // 过滤掉 padCode 为空的
                    .collect(Collectors.toMap(PadTaskBO.PadSubTaskBO::getPadCode, subTask -> subTask, (oldValue, newValue) -> newValue));

            for (NetStorageDTO netStorageDTO : netStorageDTOlist) {
                netStorageDTO.getNetStoragePadCodeDetailDTOList().forEach(netStoragePadCodeDetailDTO -> {
                    String padCode = netStoragePadCodeDetailDTO.getPadCode();
                    VirtualizeDeviceInfoVO virtualizeDeviceInfoVOS = null;
                    try {
                        //获取规格信息
                        ResourceSpecification resourceSpecification = netStoragePadHelper.getResourceSpecification(netStorageDTO.getDeviceLevel());
                        //添加cache
                        virtualizeDeviceInfoVOS = netStoragePadHelper.getVirtualizeDevice(netStoragePadCodeDetailDTO.getNetStorageComputeUnit().getDeviceId());
                        virtualizeDeviceNewNetByPadDetail(virtualizeDeviceInfoVOS, resourceSpecification, netStoragePadCodeDetailDTO.getPadDetailsVO(), subTaskMap);
                    } catch (Exception e) {
                        //回写CMS云机任务结果
                        PadTaskBO.PadSubTaskBO padSubTaskBO = subTaskMap.get(padCode);
                        PadTask padTask = new PadTask();
                        padTask.setId(padSubTaskBO.getSubTaskId());
                        padTask.setStatus(TaskStatusConstants.FAIL_ALL.getStatus());
                        padTask.setErrorMsg("云机开机失败");
                        padTask.setEndTime(new Date());
                        //任务置为失败
                        padTaskMapper.updateById(padTask);
                        UpdateSubTaskDTO taskDTO = new UpdateSubTaskDTO();
                        taskDTO.setMasterTaskId(padSubTaskBO.getMasterTaskId());
                        taskDTO.setSubTaskId(padSubTaskBO.getSubTaskId());
                        taskDTO.setSubTaskStatus(TaskStatusConstants.FAIL_ALL.getStatus());
                        taskService.updateSubTaskStatus(taskDTO);

                        // 更新网存pad的id,如果不更新ip会导致ip冲突,多个实例占用同一个ip
                        updatePadIpOnStartupFailure(padCode, netStoragePadCodeDetailDTO, virtualizeDeviceInfoVOS);

                        log.error(">>>>>>>>>>>>>>>>云机开机失败,device={}", JSON.toJSONString(netStoragePadCodeDetailDTO), e);
                    } finally {
                        List<String> padIps = padMapper.getPadIpsByDeviceId(netStoragePadCodeDetailDTO.getNetStorageComputeUnit().getDeviceId());
                        for (String padIp : padIps) {
                            String key = RedisKeyPrefix.PAD_IP_LOCK + padIp;
                            redisService.deleteObject(key);
                        }
                    }
                    ;
                });
                //保存实例跟算力,网存的映射关系
                netStoragePadHelper.saveNetStorageDTOList(netStorageDTOlist);
            }
        }

        return padTaskBOList.stream()
                .map(GeneratePadTaskVO::builder)
                .flatMap(List::stream)
                .collect(Collectors.toList()).get(0);
    }

    private Map<String, String> conversionStrToMap(String str) {
        Map<String, String> resultMap = new HashMap<>();
        if (str == null || str.trim().isEmpty()) {
            return resultMap;
        }

        ObjectMapper mapper = new ObjectMapper();
        List<Map<String, String>> list;
        try {
            list = mapper.readValue(str, List.class);
        } catch (JsonProcessingException e) {
            return resultMap;
        }

        for (Map<String, String> item : list) {
            resultMap.put(item.get("propertiesName"), item.get("propertiesValue"));
        }
        return resultMap;
    }
    private void checkNetStorageResUnitCode(NetStorageNetWorkOffDTO param) throws BasicException {
        if(StringUtils.isEmpty(param.getNetStorageResUnitCode())){
            return ;
        };
        LambdaQueryWrapper<NetStorageResUnit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NetStorageResUnit::getNetStorageResUnitCode, param.getNetStorageResUnitCode()).last("LIMIT 1");
        NetStorageResUnit netStorageResUnit = netStorageResUnitService.getOne(queryWrapper);
        PadImageDetailVO targetPadImage = null;
        if(Objects.nonNull(netStorageResUnit)){
            targetPadImage = padMapper.getImageIdByPadCode(netStorageResUnit.getPadCode());
        }
        //找不到目标实例
        if(Objects.isNull(targetPadImage)){
            return ;
        }
        PadImageDetailVO nowPadImageDetailVO = padMapper.getImageIdByPadCode(param.getPadCode());

        Map<String, String> targetMap = conversionStrToMap(targetPadImage.getPadProperties());
        Map<String, String> nowMap = conversionStrToMap(nowPadImageDetailVO.getPadProperties());

        //镜像版本不一致
        if(!Objects.equals(nowPadImageDetailVO.getImageVersion(),targetPadImage.getImageVersion())){
            throw new BasicException(PadExceptionCode.NETWORK_RES_UNIT_IMAGES_SHUTDOWN_ON);
        };
        //实例类型不一致
        if(!Objects.equals(nowPadImageDetailVO.getType(),targetPadImage.getType())){
            throw new BasicException(PadExceptionCode.NETWORK_RES_UNIT_TYPE_SHUTDOWN_ON);
        };
        //adi模板跟镜像版本模板不一样
        if(StringUtils.isNotEmpty(nowPadImageDetailVO.getAdiVersion()) && !Objects.equals(nowPadImageDetailVO.getAdiVersion(),targetPadImage.getAdiVersion())){
            throw new BasicException(PadExceptionCode.NETWORK_RES_UNIT_ADI_SHUTDOWN_ON);
        }
        //真机不允许跨品牌型号
        if(Objects.equals(nowPadImageDetailVO.getType(),(PadConstants.Type.REAL.getValue())) && !nowMap.isEmpty()){

            //品牌不相同或者型号不相同
            if(!Objects.equals(nowMap.get("ro.product.manufacturer"),targetMap.get("ro.product.manufacturer")) || !Objects.equals(nowMap.get("ro.product.model"),targetMap.get("ro.product.model"))){
                throw new BasicException(PadExceptionCode.NETWORK_RES_UNIT_MODEL_SHUTDOWN_ON);
            };
        }




        return ;
    };

    @Override
    public List<GeneratePadTaskVO> netStoragePadDelete(NetStorageResPadDeleteDTO param, SourceTargetEnum paas) {
        List<NetStorageResUnitDeleteDTO> unitDeleteDTOList = netStoragePadHelper.processNetStoragePadDelete(param);

        // 直接组装任务数据并写入数据库，不调用addReplacePadTask
        List<GeneratePadTaskVO> taskVOList = createNetStorageDeleteTaskData(param.getCustomerId(),
                param.getNetStorageResUnitCodes(), unitDeleteDTOList, paas);

        // 使用批量删除方法
        netStoragePadHelper.processNetStorageResUnitBatchDeleteCallback(param.getNetStorageResUnitCodes());
        return taskVOList;
    }

    /**
     * 创建网存删除任务数据并写入数据库
     *
     * @param customerId 客户ID
     * @param netStorageResUnitCodes 网存资源单元编码列表
     * @param unitDeleteDTOList 删除DTO列表
     * @param sourceTarget 任务来源
     * @return 任务VO列表
     */
    private List<GeneratePadTaskVO> createNetStorageDeleteTaskData(Long customerId,
            List<String> netStorageResUnitCodes, List<NetStorageResUnitDeleteDTO> unitDeleteDTOList,
            SourceTargetEnum sourceTarget) {

        if (CollUtil.isEmpty(netStorageResUnitCodes)) {
            return Collections.emptyList();
        }

        // 1. 创建主任务数据
        Task masterTask = new Task();
        masterTask.setType(CONTAINER_NET_STORAGE_RES_UNIT_DELETE.getType());
        masterTask.setStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
        masterTask.setCustomerId(customerId);
        masterTask.setTaskSource(sourceTarget != null ? sourceTarget.getCode() : SourceTargetEnum.PAAS.getCode());
        masterTask.setCreateBy(String.valueOf(customerId));
        masterTask.setUpdateBy(String.valueOf(customerId));

        // 插入主任务
        taskMapper.insertTask(masterTask);
        masterTask.setUniqueId(IdGenerateUtils.generationMasterUniqueId(masterTask.getId()));
        taskMapper.batchUpdateUniqueId(Collections.singletonList(masterTask));

        // 2. 创建子任务数据
        List<PadTask> subTaskList = new ArrayList<>();
        List<GeneratePadTaskVO> resultList = new ArrayList<>();

        for (String netStorageResUnitCode : netStorageResUnitCodes) {
            // 获取客户任务ID
            Integer customerTaskId = taskService.getCustomerTaskId(customerId);

            PadTask padTask = new PadTask();
            padTask.setPadCode(netStorageResUnitCode);
            padTask.setTaskId(masterTask.getId());
            padTask.setStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
            padTask.setCustomerId(customerId);
            padTask.setCustomerTaskId(customerTaskId);
            padTask.setCreateBy(String.valueOf(customerId));
            padTask.setUpdateBy(String.valueOf(customerId));

            subTaskList.add(padTask);

            // 构建返回的VO
            GeneratePadTaskVO taskVO = new GeneratePadTaskVO();
            taskVO.setTaskId(customerTaskId);
            taskVO.setPadCode(netStorageResUnitCode);
            taskVO.setVmStatus(1); // 默认在线状态
            resultList.add(taskVO);
        }

        // 批量插入子任务
        if (!subTaskList.isEmpty()) {
            padTaskMapper.insertBatch(subTaskList);

            // 生成子任务唯一ID并更新
            for (PadTask padTask : subTaskList) {
                padTask.setUniqueId(IdGenerateUtils.generationSubUniqueId(padTask.getId()));
            }
            padTaskMapper.batchUpdateUniqueId(subTaskList);
        }

        return resultList;
    }



    @Override
    public List<GeneratePadTaskVO> netStorageResBootOff(NetWorkOffDTO param, SourceTargetEnum sourceTargetEnum) {
        PadDetailsDTO dto = new PadDetailsDTO();
        dto.setPadCodes(param.getPadCodes());
        List<PadDetailsVO> detailsVOList = padMapper.selectDetailsByPadCode(dto);
        //过滤掉已经关机的实例跟开机中的实例,以及非网存的实例
        detailsVOList.removeIf(padDetailsVO ->{
            List<Integer>  typeList= com.google.common.collect.Lists.newArrayList(CONTAINER_NET_STORAGE_ON.getType());
            PadTask padTask = padTaskMapper.getLatestTask(padDetailsVO.getPadCode(),typeList);
            //有开机任务执行中,也不允许关机
            if(!Objects.isNull(padTask)){
                return true;
            }
            List<PadTask> lastNCompletedTasks = padTaskMapper.getLastNCompletedTasks(padDetailsVO.getPadCode());

            //判断连续三次开机失败的任务,不允许关机,发送钉钉告警
            boolean isValid = lastNCompletedTasks.size() >= 3 &&
                    lastNCompletedTasks.stream()
                            .allMatch(task -> Objects.equals(CONTAINER_NET_STORAGE_OFF.getType(),task.getType()) && !Arrays.asList(TaskStatusConstants.WAIT_EXECUTE.getStatus(), TaskStatusConstants.EXECUTING.getStatus(), TaskStatusConstants.SUCCESS.getStatus()).contains(task.getStatus()));
            if (isValid &&StringUtils.isEmpty(getOrWriteToCache(padDetailsVO.getPadCode()))) {
                String message = String.format("网存实例连续关机失败,请检查实例详情. padCode: %s", padDetailsVO.getPadCode());
                DingTalkRobotClient.sendMessage(springProfilesActive, message);
                return true;
            }

            // 判断是否存在正在关机的任务
            PadTask offTask = padTaskMapper.getLatestPadTaskByType(CONTAINER_NET_STORAGE_OFF.getType(),padDetailsVO.getPadCode());
            if(Objects.nonNull(offTask) && (offTask.getStatus().equals(TaskStatusConstants.WAIT_EXECUTE.getStatus())
                    || offTask.getStatus().equals(TaskStatusConstants.EXECUTING.getStatus()))){
                throw new BasicException(POD_HAS_RUNNING_OFF_TASK);
            }

            return Arrays.asList(PadStatusConstant.OFF, PadStatusConstant.ON_RUN).contains(padDetailsVO.getPadStatus())
                    ||Objects.equals(padDetailsVO.getNetStorageResFlag(),0)
                    ||StringUtils.isEmpty(padDetailsVO.getDeviceIp()) ;
        });

        //没有可以关机的实例
        if (CollectionUtils.isEmpty(detailsVOList)) {
            throw new BasicException(POD_OFFLINE_OFFLINE);
        }
        InstanceRestartRequest request = new InstanceRestartRequest();
        ArrayList<InstanceRestartRequest.Instance> list = Lists.newArrayList();
        request.setInstances(list);
        detailsVOList.forEach(padDetailsVO -> {
            InstanceRestartRequest.Instance instance = new InstanceRestartRequest.Instance();
            instance.setPadCode(padDetailsVO.getPadCode());
            instance.setDeviceIp(padDetailsVO.getDeviceIp());
            list.add(instance);
        });
//        List<PadEdgeClusterVO> padEdgeClusterVOS = padMapper.getPadEdgeClusterInfosByPadCodes(Collections.singletonList(param.getPadCodes().get(0)));
//        URI host = ContainerFeignUtils.builderHost(padEdgeClusterVOS.get(0).getClusterPublicIp());
        PadTaskBO padTaskBO = padTaskComponent.addPadTask(param.getCustomerId(), detailsVOList.stream().map(PadDetailsVO::getPadCode).distinct().collect(Collectors.toList()), CONTAINER_NET_STORAGE_OFF,
                null,  JSON.toJSONString(list), sourceTargetEnum);
        // 绑定CMS任务ID
        List<GeneratePadTaskVO> result = GeneratePadTaskVO.builder(padTaskBO);
        return result;
    }

    @Override
    public List<GeneratePadTaskVO> netStorageResDelete(NetWorkDeleteDTO param, SourceTargetEnum sourceTargetEnum) {
        PadDetailsDTO dto = new PadDetailsDTO();
        dto.setPadCodes(param.getPadCodes());
        dto.setStatus(1);
        List<PadDetailsVO> allDetailsVOList = padMapper.selectDetailsByPadCode(dto);
        
        // 检查是否有实例不存在
        if (CollectionUtils.isEmpty(allDetailsVOList)) {
            throw new BasicException(PAD_CODE_NOT_EXIST);
        }
        
        List<PadDetailsVO> detailsVOList = new ArrayList<>(allDetailsVOList);
        
        //过滤掉非关机的实例
        detailsVOList.removeIf(padDetailsVO -> !Objects.equals(padDetailsVO.getPadStatus(), PadStatusConstant.OFF)
                && !Objects.equals(padDetailsVO.getPadStatus(), PadStatusConstant.OFF_ERROR)// todo 临时解决关机失败实例不能删除问题
                && !Objects.equals(padDetailsVO.getPadStatus(), PadStatusConstant.ON_ERROR));
        //没有已关机的实例
        if (CollectionUtils.isEmpty(detailsVOList)) {
            throw new BasicException(POD_DELETE_LINE_ONLINE);
        }
        InstanceRestartRequest request = new InstanceRestartRequest();
        ArrayList<InstanceRestartRequest.Instance> list = Lists.newArrayList();
        request.setInstances(list);
        detailsVOList.forEach(padDetailsVO -> {
            //取最后一次开机所在的板卡
            LambdaQueryWrapper<NetStorageResOffLog> logLambdaQueryWrapper = new LambdaQueryWrapper<>();
            logLambdaQueryWrapper.eq(NetStorageResOffLog::getPadCode, padDetailsVO.getPadCode())
                    .orderByDesc(NetStorageResOffLog::getNetStorageResOffLogId)  // 按 id 倒序排序
                    .last("LIMIT 1");  // 取出第一条记录
            NetStorageResOffLog latestLog = netStorageResOffLogService.getOne(logLambdaQueryWrapper);
            Device device = null;
            if(Objects.nonNull(latestLog)){
                //验证板卡在线
                LambdaQueryWrapper<Device> deviceLambdaQueryWrapper = new LambdaQueryWrapper<>();
                deviceLambdaQueryWrapper.eq(Device::getDeviceIp, latestLog.getDeviceIp()).eq(Device::getDeleteFlag, ZERO).eq(Device::getDeviceStatus,1).last("LIMIT 1");
                device = deviceMapper.selectOne(deviceLambdaQueryWrapper);
            };
            InstanceRestartRequest.Instance instance = new InstanceRestartRequest.Instance();
            instance.setPadCode(padDetailsVO.getPadCode());
            instance.setClusterCode(padDetailsVO.getClusterCode());
            instance.setNetStorageResId(padDetailsVO.getNetStorageResId());
            //能取到,取最后一次所在的板卡,并且板卡在线
            if (Objects.nonNull(latestLog) &&Objects.nonNull(device)) {
                instance.setDeviceIp(latestLog.getDeviceIp());
            }else {
                //从来没有开机过,从该集群下随机挑一个板卡,先不挑用户,因为可能该用户没有算力.
                DeviceInfoVo deviceInfoVo = netStorageComputeUnitService.obtainAComputingIPAddressInTheCluster(padDetailsVO.getClusterCode());
                instance.setDeviceIp(deviceInfoVo.getDeviceIp());
            };
            list.add(instance);
        });
        PadTaskBO padTaskBO = padTaskComponent.addPadTask(param.getCustomerId(),detailsVOList.stream().map(PadDetailsVO::getPadCode).distinct().collect(Collectors.toList()), TaskTypeConstants.CONTAINER_NET_STORAGE_DELETE,
                null, JSON.toJSONString(request), sourceTargetEnum);
        // 绑定CMS任务ID
        List<GeneratePadTaskVO> result = GeneratePadTaskVO.builder(padTaskBO);
        return result;
    }

    /**
     * 验证实例规格是否存在
     *
     * @param deviceLevel
     * @return
     */
    private Boolean checkDeviceLevel(String deviceLevel) {
        ResourceSpecification resourceSpecification = resourceSpecificationMapper.selectOne(new QueryWrapper<ResourceSpecification>().lambda()
                .eq(ResourceSpecification::getSpecificationCode, deviceLevel)
                .eq(ResourceSpecification::getDeleteFlag, ZERO)
                .eq(ResourceSpecification::getStatus, ONE)
                .last("LIMIT 1"));
        return Objects.isNull(resourceSpecification);
    }


    /**
     * 验证当前存储规格
     *
     * @param storageCapacity
     * @return
     */
    private Boolean checkStorageCapacity(Integer storageCapacity) {
        List<Integer> list = new ArrayList<>(Arrays.asList(4, 16, 32, 64, 128, 256));
        return !list.contains(storageCapacity);
    }

    @Override
    public String netStorageResMoreCompatible(NetStorageResMoreCompatiblePaasDTO param, SourceTargetEnum sourceTargetEnum) {
        if (Objects.isNull(param.getStorageCapacity()) && StringUtils.isEmpty(param.getDeviceLevel())) {
            throw new BasicException(PadExceptionCode.NETWORK_INSTANCE_UPDATE_PARAM_NOT_EMPTY);
        }
        if (StringUtils.isNotEmpty(param.getDeviceLevel())) {
            if (checkDeviceLevel(param.getDeviceLevel())) {
                throw new BasicException(PadExceptionCode.NETWORK_INSTANCE_DEVICE_LEVEL_NOT_EXIST_REQUIRED_MESSAGE);
            }
        }
        if (Objects.nonNull(param.getStorageCapacity())) {
            if (checkStorageCapacity(param.getStorageCapacity())) {
                throw new BasicException(PadExceptionCode.NETWORK_INSTANCE_STORAGE_CAPACITY_NOT_EXIST_REQUIRED_MESSAGE);
            }
        }

        LambdaQueryWrapper<Pad> padWrapper = new LambdaQueryWrapper<>();
        padWrapper.in(Pad::getPadCode, param.getPadCodes());
        List<Pad> padList = padMapper.selectList(padWrapper);
        List<PadStatus> padStatusList = padStatusMapper.listByPadCodes(param.getPadCodes());
        //验证状态不为18的
        padStatusList.removeIf(padStatus -> Objects.equals(padStatus.getPadStatus(), 18));
        if (!CollectionUtils.isEmpty(padStatusList)) {
            throw new BasicException(PadExceptionCode.NETWORK_INSTANCE_SHUTDOWN_REQUIRED_MESSAGE);
        }
        // 检查是否有正在执行的同步备份任务
        for (String padCode : param.getPadCodes()) {
            PadTask padTask = padTaskMapper.getLatestPadTaskByType(TaskTypeConstants.NET_SYNC_BACKUP.getType(),padCode);
            if (Objects.nonNull(padTask) && (padTask.getStatus().equals(TaskStatusConstants.EXECUTING.getStatus()))) {
                throw new BasicException(NET_SYNC_DATA_EXECUTING);
            }
        }

        //只修改规格
        if (Objects.isNull(param.getStorageCapacity())) {
            padList.forEach(padVO -> padVO.setDeviceLevel(param.getDeviceLevel()));
            padMapper.batchUpdatePads(padList);
            return StringUtils.EMPTY;
        }
        Long customerId = padList.get(0).getCustomerId();
        Boolean storageCapacityNonNull = Objects.nonNull(param.getStorageCapacity());
        AtomicLong sumSize = new AtomicLong(0L);
        List<NetStorageResUnit> netStorageResUnitList = Lists.newArrayList();
        padList.forEach(padVO -> {
            //构建一个待更新的存储对象
            NetStorageResUnit storageResUnit = new NetStorageResUnit();
            storageResUnit.setNetStorageResUnitCode(padVO.getNetStorageResId());
            storageResUnit.setNetStorageResUnitSize((long)param.getStorageCapacity());
            netStorageResUnitList.add(storageResUnit);
            //网存实例缩小
            if (storageCapacityNonNull && padVO.getNetStorageResSize() > param.getStorageCapacity()) {
                throw new BasicException(PadExceptionCode.NETWORK_INSTANCE_CAPACITY_REDUCTION_NOT_ALLOWED_MESSAGE);
            }
            Long storageResSize = padVO.getNetStorageResSize();
            sumSize.addAndGet(param.getStorageCapacity() - (storageResSize != null ? storageResSize : 0L));
            if (StringUtils.isNotEmpty(param.getDeviceLevel())) {
                //真机不允许更换规格
                if (Objects.equals(padVO.getType(), PadConstants.Type.REAL.getValue())) {
                    throw new BasicException(PadExceptionCode.NETWORK_INSTANCE_REAL_NO_UPDATE_TYPE);
                }
                padVO.setDeviceLevel(param.getDeviceLevel());
            }
            if (Objects.nonNull(param.getStorageCapacity())) {
                padVO.setNetStorageResSize((long) param.getStorageCapacity());
            }
        });
        //更新网存余额
        if (!netStorageResService.deductTheSpecifiedBalanceSize(customerId, sumSize.get())) {
            throw new BasicException(PadExceptionCode.NET_STORAGE_INSUFFICIENT_CAPACITY);
        }
        //更新存储大小
        netStorageResUnitService.updateNetStorageResUnitByCode(netStorageResUnitList);
        //更新pad表
        padMapper.batchUpdatePads(padList);
        return StringUtils.EMPTY;
    }

    @Override
    public String unbindTheCardInformation(PadStatusDTO param) {
        log.info("释放算力，param: {}", JSONObject.toJSONString(param));
        Pad pad = new Pad();
        pad.setPadCode(param.getPadCode());
        LambdaQueryWrapper<Pad> padWrapper = new LambdaQueryWrapper<>();
        padWrapper.eq(Pad::getPadCode, param.getPadCode());
        Pad selectOne = padMapper.selectOne(padWrapper);
        if (Objects.nonNull(selectOne)) {
            DevicePad devicePad = devicePadMapper.selectByPadId(selectOne.getId());
            if (Objects.isNull(devicePad)) {
                log.info("PadServiceImpl.unbindTheCardInformation devicePad is null, param: {}", JSONObject.toJSONString(param));
                return StringUtils.EMPTY;
            }
            //服务器跟实例ip dns信息置空
            selectOne.setArmServerCode(StringUtils.EMPTY);
            selectOne.setPadIp(StringUtils.EMPTY);
//            selectOne.setDns(StringUtils.EMPTY);
            padMapper.updateById(selectOne);
            Long deviceId = devicePad.getDeviceId();
            LambdaQueryWrapper<DevicePad> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DevicePad::getPadId, selectOne.getId());
            //获取板卡Id
            //删除实例跟板卡绑定关系
            devicePadMapper.delete(wrapper);
            List<DevicePad> devicePadList = devicePadMapper.selectList(deviceId);
            //如果该板卡没有实例绑定,修改该板卡实例状态
            if (CollectionUtils.isEmpty(devicePadList)) {
                //修改为未分配
                deviceMapper.updatePadAllocationStatusById(Lists.newArrayList(deviceId), 0);
            }
        } else {
            log.info("PadServiceImpl.unbindTheCardInformation pad is null, param: {}", JSONObject.toJSONString(param));
        }
        //处理实例关机成功存储跟算力回收
        netStoragePadHelper.processNetStoragePadOff(param.getPadCode());
        return StringUtils.EMPTY;
    }

    @Override
    public String deletePadInformation(PadStatusDTO param) {
        LambdaQueryWrapper<Pad> padDelete = new QueryWrapper<Pad>().lambda().eq(Pad::getPadCode, param.getPadCode()).eq(Pad::getStatus,1).last("LIMIT 1");
        Pad pad = padMapper.selectOne(padDelete);
        if (Objects.isNull(pad)) {
            log.error("deletePadInformation select pad result empty! param:{}",JSON.toJSONString(param));
            return StringUtils.EMPTY;
        }
        pad.setStatus(-1);
        //逻辑删除
        padMapper.updateById(pad);

        // padStatus状态也要更新为软删除
        PadStatus padStatus = new PadStatus();
        padStatus.setPadCode(param.getPadCode());
        padStatus.setPadStatus(-1);
        padStatusMapper.updateBatchPadStatus(Collections.singletonList(padStatus));

        // 检查是否有正在执行的同步备份任务
        PadTask padTask = padTaskMapper.getLatestPadTaskByType(TaskTypeConstants.NET_SYNC_BACKUP.getType(),param.getPadCode());
        if (Objects.nonNull(padTask) && (padTask.getStatus().equals(TaskStatusConstants.FAIL_ALL.getStatus())
                || padTask.getStatus().equals(TaskStatusConstants.FAIL_PART.getStatus())
                || padTask.getStatus().equals(EXECUTING.getStatus())
                || padTask.getStatus().equals(WAIT_EXECUTE.getStatus()))){
            // 网存同步失败时不会清除算力,这里要释放算力
            log.info("删除实例后释放算力, param: {}",JSON.toJSONString(param));
            unbindTheCardInformation(param);
        }
        //解绑实例跟板卡的关系
//        unbindTheCardInformation(param);
        String lockKey = LockKeyConstants.NetStorageProcess.NET_STORAGE_RES_LOCK_KEY_PREFIX + pad.getCustomerId() ;
        RLock rLock = redissonDistributedLock.tryLock(lockKey, 10, 60);
        if (Objects.isNull(rLock)) {
            log.error("deletePadInformation get lock error, param:{}",JSON.toJSONString(param));
            return StringUtils.EMPTY;
        }
        try{
            LambdaQueryWrapper<NetStorageResPad> lambdaQueryWrapper = new LambdaQueryWrapper<NetStorageResPad>().eq(NetStorageResPad::getPadCode, param.getPadCode()).last("LIMIT 1");
//            NetStorageResPad netStorageResPad = netStorageResPadMapper.selectOne(lambdaQueryWrapper);
            //删除实例跟用户购买网存的关联性
            netStorageResPadMapper.delete(lambdaQueryWrapper);
            LambdaQueryWrapper<NetStorageResUnit> netStorageResUnitLambdaQueryWrapper = new LambdaQueryWrapper<NetStorageResUnit>().eq(NetStorageResUnit::getNetStorageResUnitCode, pad.getNetStorageResId()).last("LIMIT 1");
            //删除存储信息
            netStorageResUnitService.remove(netStorageResUnitLambdaQueryWrapper);
//            LambdaQueryWrapper<NetStorageRes> netWrapper = new LambdaQueryWrapper<>();
//            netWrapper.eq(NetStorageRes::getNetStorageResId, netStorageResPad.getNetStorageResId()).last("LIMIT 1");
            //找到该实例所属用户的网存id,然后更新该用户网存的容量
//            NetStorageRes lastNetStorageRes = netStorageResMapper.selectOne(netWrapper);
//            if (lastNetStorageRes != null) {
//                long size = lastNetStorageRes.getStorageCapacityUsed() - pad.getNetStorageResSize();
//                //将该pad使用的内存加回去
//                if(size>0){
//                    lastNetStorageRes.setStorageCapacityUsed(size);
//                    netStorageResMapper.updateById(lastNetStorageRes); // 更新记录
//                }
//            }
            //可能会有空指针,历史数据没有完全处理正确.这里可能找不到开机跟存储的关机映射,直接通过用户id去找
        }catch (Exception e) {
            log.error("{} deletePadInformation process error, param:{} ,e:{}",this.getClass().getName(),JSON.toJSONString(param),e.getMessage(),e);
//            LambdaQueryWrapper<NetStorageRes> netWrapper = new LambdaQueryWrapper<>();
//            netWrapper.eq(NetStorageRes::getCustomerId, pad.getCustomerId()).last("LIMIT 1");
//            //通过用户id去找存储信息,目前只会存在一个用户的网存信息,所以这里不会查到多个结果.查到多个结果说明数据有问题
//            NetStorageRes netStorageRes = netStorageResMapper.selectOne(netWrapper);
//            if (netStorageRes != null) {
//                //将该pad使用的内存加回去
//                long size = netStorageRes.getStorageCapacityUsed() - pad.getNetStorageResSize();
//                if(size>0){
//                    netStorageRes.setStorageCapacityUsed(size);
//                    netStorageResMapper.updateById(netStorageRes); // 更新记录
//                }
//            }
        }finally {
            if(rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }

        //删除房间相关信息
        return StringUtils.EMPTY;
    }

    @Override
    public StorageCapacityDetailVO getDetailStorageCapacityAvailable(NetStorageResDetailDTO param) {
        StorageCapacityDetailVO resultBean = new StorageCapacityDetailVO();
        LambdaQueryWrapper<NetStorageRes> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NetStorageRes::getCustomerId, param.getCustomerId());
        if (StringUtils.isNotBlank(param.getClusterCode())) {
            wrapper.eq(NetStorageRes::getClusterCode, param.getClusterCode());
        }
        List<NetStorageRes> list = netStorageResService.list(wrapper);
        if (list == null || list.isEmpty()) {
            return resultBean;
        }

        // 计算总容量、已使用容量和可用容量
        long storageCapacityTotal = list.stream()
                .mapToLong(netStorageRes -> Optional.ofNullable(netStorageRes.getStorageCapacity()).orElse(0L))
                .sum();

        long storageCapacityUsedTotal = list.stream()
                .mapToLong(netStorageRes -> Optional.ofNullable(netStorageRes.getStorageCapacityUsed()).orElse(0L))
                .sum();

        long storageCapacityAvailable = storageCapacityTotal - storageCapacityUsedTotal;
        resultBean.setStorageCapacityTotal(storageCapacityTotal);
        resultBean.setStorageCapacityUsedTotal(storageCapacityUsedTotal);
        resultBean.setStorageCapacityAvailable(storageCapacityAvailable);
        resultBean.setNetStorageResList(list);
        return resultBean;
    }

    @Override
    public List<StorageCapacityDetailVO> getDetailStorageCapacityAvailableList(NetStorageResDetailDTO param) {
        LambdaQueryWrapper<NetStorageRes> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NetStorageRes::getCustomerId, param.getCustomerId());
        if (StringUtils.isNotBlank(param.getClusterCode())) {
            wrapper.eq(NetStorageRes::getClusterCode, param.getClusterCode());
        }
        List<NetStorageRes> list = netStorageResService.list(wrapper);
        if (list == null || list.isEmpty()) {
            return Lists.newArrayList();
        }
        Map<String, List<NetStorageRes>> groupedByClusterCode = list.stream()
                .collect(Collectors.groupingBy(NetStorageRes::getClusterCode));


        List<StorageCapacityDetailVO> storageCapacityDetailVOS =Lists.newArrayList();

        groupedByClusterCode.forEach((clusterCode, detailVoList) -> {
            // 计算总容量、已使用容量和可用容量
            long storageCapacityTotal = detailVoList.stream()
                    .mapToLong(netStorageRes -> Optional.ofNullable(netStorageRes.getStorageCapacity()).orElse(0L))
                    .sum();

            long storageCapacityUsedTotal = detailVoList.stream()
                    .mapToLong(netStorageRes -> Optional.ofNullable(netStorageRes.getStorageCapacityUsed()).orElse(0L))
                    .sum();
            StorageCapacityDetailVO storageCapacityDetailVO = new StorageCapacityDetailVO();
            long storageCapacityAvailable = storageCapacityTotal - storageCapacityUsedTotal;
            storageCapacityDetailVO.setStorageCapacityTotal(storageCapacityTotal);
            storageCapacityDetailVO.setStorageCapacityUsedTotal(storageCapacityUsedTotal);
            storageCapacityDetailVO.setStorageCapacityAvailable(storageCapacityAvailable);
            storageCapacityDetailVO.setNetStorageResList(detailVoList);
            storageCapacityDetailVO.setClusterCode(detailVoList.get(0).getClusterCode());
            storageCapacityDetailVOS.add(storageCapacityDetailVO);
        });
        return storageCapacityDetailVOS;
    }


    @Override
    public List<NetPadDeviceVO> groupNetPadByDeviceLevel(NetStorageResDetailDTO param) {
//        List<NetPadDeviceVO> netPadDeviceVOS = padMapper.groupNetPadByDeviceLevel(param);
        NetStorageComputeDTO computeDTO = new NetStorageComputeDTO();
        computeDTO.setCustomerId(param.getCustomerId());
        computeDTO.setClusterCode(param.getClusterCode());
        Map<String, NetStorageComputeVO> computeVOMap = netStorageComputeUnitService.retrieveNetStorageComputeUsage(computeDTO);
        List<NetPadDeviceVO> netPadDeviceVOList = computeVOMap.entrySet().stream()
                .map(entry -> {
                    NetPadDeviceVO netPadDeviceVO = new NetPadDeviceVO();
                    netPadDeviceVO.setDeviceLevel(entry.getKey()); // 从 key 赋值给 deviceLevel
                    netPadDeviceVO.setOnNumber(entry.getValue().getOnNumber()); // 绑定数量
                    netPadDeviceVO.setTotalNumber(entry.getValue().getTotalNumber()); // 总数
                    return netPadDeviceVO;
                }).collect(Collectors.toList());

        return netPadDeviceVOList;
    }


    @Override
    public List<GeneratePadTaskVO> netStoragePadBackup(NetStorageResPadBackupDTO param, SourceTargetEnum paas) {

        List<Pad> padList = padMapper.listByPadCodes(param.getPadCodes());
        List<String> resUnitCodeList = padList.stream()
                .map(Pad::getNetStorageResId)
                .filter(s -> s != null && !s.trim().isEmpty())  // 过滤 null 和空字符串
                .distinct()
                .collect(Collectors.toList());
        if(resUnitCodeList.size() ==0){
            throw new BasicException(PadExceptionCode.NETWORK_RES_UNIT_EMPTY);
        }
        LambdaQueryWrapper<NetStorageResUnit> wrapper = new LambdaQueryWrapper();
        wrapper.in(NetStorageResUnit::getNetStorageResUnitCode, resUnitCodeList).eq(NetStorageResUnit::getCustomerId, param.getCustomerId());
        //不会为空
        List<NetStorageResUnit> list = netStorageResUnitService.list(wrapper);
        //验证该用户名下的网存实例是否跟用户传入的实例相同
//        if(list.size()<resUnitCodeList){
//            throw new BasicException(PadExceptionCode.NETWORK_RES_UNIT_EMPTY);
//        }
        Map<String, List<NetStorageResUnit>> groupedByClusterCode = list.stream()
                .collect(Collectors.groupingBy(NetStorageResUnit::getClusterCode));
        //保留状态为1的(开机)
        List<NetStorageResUnit> bootTheNetworkStorageList = list.stream().filter(netStorageResUnit -> Objects.equals(netStorageResUnit.getShutdownFlag(), 1)).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(bootTheNetworkStorageList)){
            throw new BasicException(PadExceptionCode.NETWORK_RES_UNIT_SHUTDOWN_ON);
        }
        // 未释放算力不允许备份
        param.getPadCodes().forEach(item->{
            NetStorageComputeUnit netStorageComputeUnit = netStorageComputeUnitMapper.selectBoundComputeUnitByPadCode(item);
            if(Objects.nonNull(netStorageComputeUnit)){
                NetStorageComputeUnitPad netStorageComputeUnitPad = netStorageComputeUnitPadMapper.selectOne(new LambdaQueryWrapper<NetStorageComputeUnitPad>()
                        .eq(NetStorageComputeUnitPad::getNetStorageComputeUnitCode,netStorageComputeUnit.getNetStorageComputeUnitCode())
                        .orderByDesc(NetStorageComputeUnitPad::getCreateTime)
                        .last("limit 1"));
                if(item.equalsIgnoreCase(netStorageComputeUnitPad.getPadCode())){
                    throw new BasicException(PadExceptionCode.NET_DATA_SYNC_EXECUTING);
                }
            }
        });

        // 同步网存任务执行中不允许备份
        for (String padCode : param.getPadCodes()) {
            // 查询是否有执行中的备份任务（直接查询Task和PadTask表）
            boolean hasExecutingBackupTask = hasExecutingBackupTask(padCode);
            if (hasExecutingBackupTask) {
                throw new BasicException(NET_DATA_SYNC_EXECUTING);
            }
        }
        // 取消待执行的同步备份任务
        for (String padCode : param.getPadCodes()) {
            // 查找并取消待执行的同步备份任务
            cancelPendingBackupTasks(padCode);
        }
        List<NetStorageResUnit> taskList = new ArrayList<>();
        groupedByClusterCode.forEach((clusterCode, netStorageResUnitList) -> {
            NetStorageResDetailDTO dto = new NetStorageResDetailDTO();
            dto.setCustomerId(param.getCustomerId());
            dto.setClusterCode(clusterCode);
            StorageCapacityDetailVO capacityDetailVO = getDetailStorageCapacityAvailable(dto);
            //本次需要的容量大小
            long totalSize = netStorageResUnitList.stream()
                    .mapToLong(unit -> {
                        String sizeStr = unit.getNetStorageResUnitUsedSize();
                        if (sizeStr == null || sizeStr.trim().isEmpty()) {
                            return 0L;
                        }
                        try {
                            return (long) Double.parseDouble(sizeStr);
                        } catch (NumberFormatException e) {
                            return 0L; // 遇到非法字符串时，默认返回0
                        }
                    })
                    .sum();

            //验证容量是否足够
            if (capacityDetailVO.getStorageCapacityAvailable() < totalSize) {
                throw new BasicException(PadExceptionCode.NET_STORAGE_INSUFFICIENT_CAPACITY);
            }
            //备份并扣减存储
            List<NetStorageResUnit> storageResUnitList = netStoragePadHelper.padBackup(netStorageResUnitList, totalSize, clusterCode, param.getCustomerId(),param.getRemark());
            taskList.addAll(storageResUnitList);
        });
        List<String> netStorageResCodeList = taskList.stream().map(netStorageResUnit -> {
            String netStorageResUnitCode = netStorageResUnit.getNetStorageResUnitCode();
            //拼接一下前缀,兼容实例任务AC开头的获取
            return netStorageResUnitCode;
        }).collect(Collectors.toList());

        PadTaskBO padTaskBO = padTaskComponent.addReplacePadTask(param.getCustomerId(),
                netStorageResCodeList,
                CONTAINER_NET_STORAGE_BACKUP, null, JSONObject.toJSONString(taskList), paas);
        List<GeneratePadTaskVO> builder = GeneratePadTaskVO.builder(padTaskBO);
        return builder;
    }


    void virtualizeDeviceNewNetByPadDetail(VirtualizeDeviceInfoVO device, ResourceSpecification resourceSpecification, PadDetailsVO padDetailsVO, Map<String, PadTaskBO.PadSubTaskBO> subTaskMap) {
        String deviceIp = device.getDeviceIp();
        VirtualizeDeviceRequest req = new VirtualizeDeviceRequest();
        VirtualizeDeviceRequest.Device data = new VirtualizeDeviceRequest.Device();
        data.setDeviceIp(deviceIp);
        //容器初始化参数
        VirtualizeDeviceRequest.InitInformation initInformation = new VirtualizeDeviceRequest.InitInformation();
        initInformation.setHostStorageSize(device.getHostStorageSize());
        initInformation.setContainerConcurrentSize(resourceSpecification.getPadNumber());
        data.setInitInformation(initInformation);


        List<VirtualizeDeviceRequest.Device.Pad> pads = new ArrayList<>();
        data.setPads(pads);
        List<String> padCodes = new ArrayList<>();
        List<Pad> padList = Lists.newArrayList();
        List<DevicePad> devicePadList = Lists.newArrayList();
        List<NetStorageResOffLog> netStorageResOffLogArrayList = Lists.newArrayList();

        List<PadStatus> padStatusList = Lists.newArrayList();
        PadDetailsVO padInfo = padDetailsVO;
        NetStorageResOffLog storageResOffLog = new NetStorageResOffLog();
        storageResOffLog.setPadCode(padInfo.getPadCode());
        storageResOffLog.setDeviceIp(device.getDeviceIp());
        storageResOffLog.setNetStorageResId(padInfo.getNetStorageResId());
        storageResOffLog.setCreateTime(new Date());
        netStorageResOffLogArrayList.add(storageResOffLog);
        DevicePad devicePad = new DevicePad();
        devicePad.setDeviceId(device.getDeviceId());
        devicePad.setPadId(padInfo.getPadId());
        devicePadList.add(devicePad);
        PadStatus padStatus = new PadStatus();
        padStatus.setPadCode(padInfo.getPadCode());
        //padStatus.setPadStatus(PadStatusConstant.ON_RUN);
        padStatusList.add(padStatus);
        Pad pad = new Pad();
        pad.setPadCode(padInfo.getPadCode());
        VirtualizeDeviceRequest.Device.Pad padVirtualize = new VirtualizeDeviceRequest.Device.Pad();
        padVirtualize.setPadCode(padInfo.getPadCode());
        ImageRequest image = new ImageRequest();
        image.setId(padInfo.getImageId());
        padVirtualize.setNetStorageResId(padInfo.getNetStorageResId());
        // 暂不需要放开此功能，暂为固定值
        image.setTag("latest");
//            image.setTag(customerUploadImage.getImageTag());
        padVirtualize.setImage(image);
        ScreenLayout screenLayout = screenLayoutMapper.selectOne(new QueryWrapper<ScreenLayout>().eq("code", padInfo.getScreenLayoutCode()));

        DisplayRequest display = new DisplayRequest();
        display.setWidth(screenLayout.getScreenWidth().intValue());
        display.setHeight(screenLayout.getScreenHigh().intValue());
        display.setDpi(screenLayout.getPixelDensity().intValue());
        display.setFps(screenLayout.getScreenRefreshRate().intValue());
        padVirtualize.setDisplay(display);

        SpecRequest spec = new SpecRequest();
        spec.setCpu( !Objects.isNull(padInfo.getCpu()) && padInfo.getCpu()>0 ? padInfo.getCpu() : device.getCpu());
        spec.setMemory(!Objects.isNull(padInfo.getMemory())  ? padInfo.getMemory() : device.getMemory());
        spec.setDisk(padInfo.getNetStorageResSize().intValue());
        spec.setIsolateDisk(true);
        padVirtualize.setSpec(spec);

        NetworkRequest network = new NetworkRequest();
        //获取IP
        network.setIp(padDetailsVO.getPadIp());
        pad.setPadIp(padDetailsVO.getPadIp());
        //有可能指定网存Id开机,更新一下
        pad.setNetStorageResId(padInfo.getNetStorageResId());
        pad.setArmServerCode(device.getArmServerCode());
        network.setSubnet(device.getSubnet());
        network.setIpRange(device.getIpRange());
        network.setGateway(device.getGateway());
        network.setNetworkDeviceName(device.getMacVlan());
        network.setMac(padInfo.getMac());
        network.setDns(padInfo.getDns());
        padVirtualize.setNetwork(network);
        padVirtualize.setAndroidProp("ro.boot.armcloud_server_addr=" + armcloudServerAddr);

        //支持adi传入数据
        if (!ObjectUtil.isEmpty(padInfo.getRealPhoneTemplateId())) {
            RealPhoneTemplate realPhoneTemplate = realPhoneTemplateMapper.selectById(padInfo.getRealPhoneTemplateId());
            VirtualizeDeviceRequest.ADI adi = new VirtualizeDeviceRequest.ADI();
            adi.setTemplateUrl(realPhoneTemplate.getAdiTemplateDownloadUrl());
            adi.setTemplatePassword(realPhoneTemplate.getAdiTemplatePwd());
            adi.setRealPhoneTemplateId(padInfo.getRealPhoneTemplateId());
            String imageParameter = customerUploadImageMapper.getImageParameterByImageUniqueId(padInfo.getImageId());
            AdiCertificateRepository adiCertificateRepository = adiCertificateManager.useAdiCertificate(padInfo.getPadCode(), imageParameter);
            if (adiCertificateRepository != null) {
                adi.setAndroidCertData(adiCertificateRepository.getCertificate());
            }
            padVirtualize.setAdi(adi);
        }
        Map<String, String> deviceAndroidProps = new HashMap<>();
        deviceAndroidProps.put("persist.sys.cloud.madb_enable","0");
        //创建实例默认关闭adb 需要追加安卓属性
        padVirtualize.setDeviceAndroidProps(deviceAndroidProps);
        pads.add(padVirtualize);
        padCodes.add(padInfo.getPadCode());
        padList.add(pad);
        req.setDevices(Collections.singletonList(data));
        req.setNetStorageResFlag(1);

        //任务打成进行中 不需要将任务打成执行中
        PadTaskBO.PadSubTaskBO padSubTaskBO = subTaskMap.get(padDetailsVO.getPadCode());
        LambdaQueryWrapper<TaskRelInstanceDetailImageSucc> succLambdaQueryWrapper = new LambdaQueryWrapper<>();
        succLambdaQueryWrapper.eq(TaskRelInstanceDetailImageSucc::getInstanceName,padDetailsVO.getPadCode()).last("ORDER BY id DESC LIMIT 1");
        TaskRelInstanceDetailImageSucc imageSucc = taskRelInstanceDetailImageSuccMapper.selectOne(succLambdaQueryWrapper);

//        if(Objects.isNull(imageSucc)){
        //首次开机，写入succ表
        taskService.saveDeviceInstance(padSubTaskBO.getMasterTaskId(),padSubTaskBO.getSubTaskId(), TaskTypeEnum.INSTANCE_NET_WORK_ON,req.getDevices().get(0),device,true);
//        }
        //提前写入板卡跟实例映射关系
        devicePadMapper.batchInsert(devicePadList);
        //更新板卡实例分配状态为已分配
        deviceMapper.updatePadAllocationStatusById(devicePadList.stream().map(DevicePad::getDeviceId).collect(Collectors.toList()), 2);
//            padStatusMapper.updateBatchPadStatus(padStatusList);
        //添加开机日志记录表
        netStorageResOffLogService.saveBatch(netStorageResOffLogArrayList);
        //更新IP
        padMapper.batchUpdatePadIp(padList);
    }

    /**
     * 获取未使用的IP列表
     *
     * @param armServerId
     * @param padNumber
     * @return
     */
    private List<String> getIpList(Long armServerId, int padNumber) {
        List<String> ips = Lists.newArrayList();
        List<String> ipv4Cidrs = netPadMapper.selectIpv4CidrsByArmServer(armServerId);
        if (ipv4Cidrs.isEmpty()) {
            throw new BasicException(NETWORK_SEGMENT_NOT_EXIST);
        }
        List<String> ipCidrs = new ArrayList<>();
        for (String ipv4Cidr : ipv4Cidrs) {
            Set<String> ipAddressesFromCIDR;
            if (DeviceServiceImpl.ipSubNumWhiteList != null && DeviceServiceImpl.ipSubNumWhiteList.size() > 0) {
                log.info(">>>>>> ipSubNumWhiteList enabled value is {} ", DeviceServiceImpl.ipSubNumWhiteList);
                ipAddressesFromCIDR = CIDRUtils.getIPAddressesFromCIDR(ipv4Cidr, DeviceServiceImpl.ipSubNumWhiteList);
            } else {
                ipAddressesFromCIDR = CIDRUtils.getIPAddressesFromCIDR(ipv4Cidr);
            }
            if (ipAddressesFromCIDR != null && !ipAddressesFromCIDR.isEmpty()) {
                ipCidrs.addAll(ipAddressesFromCIDR);
            }
        }
        ips = CIDRUtils.sortIPs(ipCidrs);
        List<String> unusedIps = new ArrayList<>();
        List<String> usedPadIps = padMapper.selectUsePadIps(ips);
        if (CollUtil.isNotEmpty(ips)) {
            for (String usedPadIp : ips) {
                String key = RedisKeyPrefix.PAD_IP_LOCK + usedPadIp;
                if (!usedPadIps.contains(usedPadIp)
                ) {
                    redisService.setCacheObject(key, usedPadIp, RedisKeyTime.minute_5, TimeUnit.MINUTES);
                    unusedIps.add(usedPadIp);
                }
                if (unusedIps.size() == padNumber) {
                    break;
                }
            }
        }
        if (unusedIps.size() < padNumber) {
            throw new BasicException(PAD_IP_NOT_ENOUGH);
        }
        return unusedIps;
    }

    /**
     * 创建实例
     *
     * @param param
     * @param resourceSpecification
     * @param screenLayout
     */
    List<Pad> virtualizeDeviceNewNet(NetWorkVirtualizeDTO param, ResourceSpecification resourceSpecification, ScreenLayout screenLayout) {
        List<Pad> extracted = extractedPadNet(param, resourceSpecification, screenLayout);
        return extracted;
    }

    @Override
    public List<GeneratePadTaskInfoVO> replaceRealAdbTemplate(ReplaceRealAdbTemplateDTO param,
                                                              SourceTargetEnum sourceTargetEnum) {
        List<Pad> padList = padMapper.selectList(new QueryWrapper<Pad>().in("pad_code", param.getPadCodes()));

        if (CollectionUtil.isEmpty(padList)) {
            throw new BasicException(PAD_CODE_NOT_EXIST);
        }
        Set<Pad> typeSet = padList.stream()
                .filter(pad -> Objects.equals(pad.getType(), PadConstants.Type.VIRTUAL.getValue()))
                .collect(Collectors.toSet());
        if (typeSet.size() > 0) {
            throw new BasicException(REAL_PHONE_TEMPLATE_NOT_EXIST);
        }
        RealPhoneTemplate realPhoneTemplate = realPhoneTemplateMapper.selectById(param.getRealPhoneTemplateId());
        // 镜像版本号
        if (realPhoneTemplate == null || realPhoneTemplate.getDeleteFlag()) {
            throw new BasicException(REAL_PHONE_ADI_NOT_EXIST);
        }
        // 校验当前ADI模板权限问题
        if(!redisService.isAdmin(param.getCustomerId()) && realPhoneTemplate.getIsPublic().equals(0)){
            Long count = adiTemplateCustomerMapper.selectCount(new LambdaQueryWrapper<AdiTemplateCustomer>()
                    .eq(AdiTemplateCustomer::getCustomerId,param.getCustomerId())
                    .eq(AdiTemplateCustomer::getTemplateId,param.getRealPhoneTemplateId()));
            if(count == 0){
                throw new BasicException(REAL_PHONE_ADI_NOT_RIGHT);
            }
        }
        // 检查是否禁用
        if (realPhoneTemplate.getStatus() == 0) {
            throw new BasicException(REAL_PHONE_ADI_UNAVAILABLE);
        }

        Integer imageVersion = realPhoneTemplate.getAndroidImageVersion();
        List<String> collect = padList.stream().map(Pad::getImageId).collect(Collectors.toList());
        QueryWrapper<CustomerUploadImage> wrapper = new QueryWrapper<>();
        wrapper.in("unique_id", collect);
        // 找到所有的镜像信息
        List<CustomerUploadImage> list = customerUploadImageMapper.selectList(wrapper);
        // 过滤掉镜像版本号一致的镜像信息
        List<CustomerUploadImage> customerUploadImageList = list.stream().filter(
                        customerUploadImage -> !customerUploadImage.getRomVersion().contains(String.valueOf(imageVersion)))
                .collect(Collectors.toList());
        log.info("customerUploadImageList_debug  customerUploadImageList:{}list:{} imageVersion:{}",
                JSONObject.toJSONString(customerUploadImageList), JSONObject.toJSONString(list), imageVersion);
        // 有pad的镜像版本跟实例的镜像版本不一致,则直接抛出异常
        if (!CollectionUtils.isEmpty(customerUploadImageList)) {
            throw new BasicException(REAL_PHONE_TEMPLATE_NOT_EXIST);
        }
        // 验证当前adi模板是否能适合当前传入的实例
//        Set<String> deviceLevelList = padList.stream().map(Pad::getDeviceLevel).collect(Collectors.toSet());
//        for (String deviceLevel : deviceLevelList) {
//            if (!deviceLevel.equals(realPhoneTemplate.getResourceSpecificationCode())) {
//                throw new BasicException(REAL_PHONE_TEMPLATE_NOT_EXIST);
//            }
//        }
        PadUpdateAdiTaskQueueBO taskQueueBO = new PadUpdateAdiTaskQueueBO();
        taskQueueBO.setSourceTarget(SourceTargetEnum.PAAS.getCode());
        taskQueueBO.setWipeData(param.getWipeData());
        String oprBy = Optional.of(param).map(ReplaceRealAdbTemplateDTO::getOprBy)
                .orElse(String.valueOf(param.getCustomerId()));
        taskQueueBO.setOprBy(oprBy);
        taskQueueBO.setAndroidProp(param.getAndroidProp());
        taskQueueBO.setAdiUrl(realPhoneTemplate.getAdiTemplateDownloadUrl());
        taskQueueBO.setAdiPassword(realPhoneTemplate.getAdiTemplatePwd());
        taskQueueBO.setRealPhoneTemplateId(realPhoneTemplate.getId());
        // 暂时不设置安卓证书.不知道有啥用
        // AdiCertificateRepository adiCertificateRepository =
        // adiCertificateManager.useAdiCertificate(padInfo.getPadCode());
        // if (adiCertificateRepository != null) {
        // adi.setAndroidCertData(adiCertificateRepository.getCertificate());
        // }
        // 获取真机模板对应的布局 用于替换容器属性
        ScreenLayout screenLayout = screenLayoutMapper
                .selectOne(new QueryWrapper<ScreenLayout>().eq("code", realPhoneTemplate.getScreenLayoutCode()));
        if (screenLayout == null || screenLayout.getDeleteFlag() == 1) {
            throw new BasicException(SCREEN_LAYOUT_NOT_EXIST);
        }
        taskQueueBO.setScreenLayoutCode(screenLayout.getCode());
        taskQueueBO.setLayoutWidth(screenLayout.getScreenWidth());
        taskQueueBO.setLayoutHigh(screenLayout.getScreenHigh());
        taskQueueBO.setLayoutDpi(screenLayout.getPixelDensity());
        taskQueueBO.setLayoutFps(screenLayout.getScreenRefreshRate());
        Consumer<AddPadTaskDTO> padTaskConsumer = addPadTaskDTO -> {
            addPadTaskDTO.setCreateBy(oprBy);
            addPadTaskDTO.setWipeData(param.getWipeData());
        };
        PadTaskBO padTaskBO = padTaskComponent.addPadTask(param.getCustomerId(),
                Lists.newArrayList(param.getPadCodes()),
                REPLACE_REAL_ADB, padTaskConsumer, JSON.toJSONString(taskQueueBO), sourceTargetEnum);
        return GeneratePadTaskInfoVO.builder(padTaskBO);
    }

    @Override
    public Boolean updatePadTypeService(PadTypeDTO padTypeDTO) {
        Pad updatePad = new Pad();
        updatePad.setType(padTypeDTO.getType());

        return padMapper.update(updatePad, new QueryWrapper<Pad>().eq("pad_code", padTypeDTO.getPadCode())) > 0;
    }

    @Override
    public GeneratePadTaskVO modifyPadProperties(ModifyPadInformationDTO param, SourceTargetEnum sourceTargetEnum) {
        List<Pad> padList = padMapper
                .selectList(new QueryWrapper<Pad>().lambda().in(Pad::getPadCode, param.getPadCodes()));
        if (CollectionUtils.isEmpty(padList)) {
            throw new BasicException("找不到对应的实例");
        }
        String queueContentJSON = null;
        ScreenLayout screenLayout = null;
        // 获取屏幕布局相关信息
        if (StringUtils.isNotEmpty(param.getScreenLayoutCode())) {
            QueryWrapper<ScreenLayout> wrapper = new QueryWrapper<>();
            wrapper.eq("code", param.getScreenLayoutCode());
            screenLayout = screenLayoutMapper.selectOne(wrapper);
            // 找不到屏幕布局
            if (Objects.isNull(screenLayout)) {
                throw new BasicException(PadExceptionCode.SCREEN_LAYOUT_NOT_EXIST.getMsg());
            }
        }
        ModifyPadPropertiesDTO modifyPadPropertiesDTO = ModifyPadPropertiesDTO.builderByModifyPadInformationDTO(param,
                screenLayout);
        // 获取参数信息
        if (!Objects.isNull(modifyPadPropertiesDTO)) {
            queueContentJSON = JSONObject.toJSONString(modifyPadPropertiesDTO);
        }
        ;
        if (StringUtils.isEmpty(queueContentJSON)) {
            throw new BasicException("参数校验失败");
        }
        List<String> padCodeList = padList.stream().map(Pad::getPadCode).collect(Collectors.toList());
        String finalQueueContentJSON = queueContentJSON;
        Consumer<AddPadTaskDTO> padTaskConsumer = addTaskDTO -> addTaskDTO.setTaskContent(finalQueueContentJSON);
        PadTaskBO padTaskBO = padTaskComponent.addPadTask(param.getCustomerId(), padCodeList,
                MODIFY_PROPERTIES_PAD,
                padTaskConsumer, queueContentJSON, sourceTargetEnum);
        return GeneratePadTaskVO.builder(padTaskBO).get(0);
    }

    @Override
    public Integer updatePadLayoutCode(PadLayoutCodeDto param) {
        return padMapper.updatePadLayoutCode(param);
    }

    @Override
    public List<GeneratePadTaskInfoVO> openOnlineAdb(OpenOnlineAdbPadTaskDTO param, SourceTargetEnum paas) {
        List<Pad> padList = this.getPadListByPadCode(param.getPadCodes());
        if (CollectionUtils.isEmpty(padList)) {
            throw new BasicException("pad not exist");
        }
        ;
        PadTaskBO padTaskBO = padTaskComponent.addPadTask(param.getCustomerId(),
                padList.stream().map(Pad::getPadCode).collect(Collectors.toList()),
                OPEN_ONLINE_PAD, null, String.valueOf(param.getStatus()), PAAS);
        return GeneratePadTaskInfoVO.builder(padTaskBO);

    }

    @Override
    public List<GeneratePadTaskVO> triggeringWhiteListService(TriggeringBlackDTO param) {
        Long customerId = param.getCustomerId();
        List<String> padCodes = padMapper.getPadCodesByDeviceLevel(param.getPadCodes(), param.getPadGrade(),
                customerId);
        List<String> whitelists = appWhiteMapper.selectWhiteAppPkgList(customerId, param.getPadGrade());

        // 新黑白名单中各实例自定义名单 key为黑白名单id value为包名白名单列表
        Map<Long, List<String>> padWhiteMap = null;
        // 新黑白名单实例和黑白名单对应关系 key为实例编号 value为白白名单id
        Map<String, List<Long>> padAppClassifyMap = null;
        if (param.getIsMergeAppClassifyList() == null || !param.getIsMergeAppClassifyList()
                || param.getIsNewApi() == null || !param.getIsNewApi()) {
            // 不合并新黑白名单或者老接口进来 则按照原逻辑进行校验
            if (CollUtil.isEmpty(whitelists)) {
                throw new BasicException(WHITELIST_NOT_EXIST);
            }

            if (CollUtil.isEmpty(padCodes)) {
                throw new BasicException(PAD_CODE_NOT_EXIST);
            }
        } else {
            // 合并新黑白名单 则按新逻辑
            padCodes = param.getPadCodes();
            // 先查出这批padCode下所有的黑名单id
            if (param.getApplyAllInstances() == null || !param.getApplyAllInstances()) {
                List<CustomerAppClassifyPadRelation> customerAppClassifyPadRelationList = customerAppClassifyPadRelationMapper
                        .selectAppClassifyPadListByType(param.getCustomerId(), padCodes, 1);
                if (CollUtil.isNotEmpty(customerAppClassifyPadRelationList)) {
                    padAppClassifyMap = new HashMap<>();
                    Set<Long> appClassifyIds = new HashSet<>();
                    for (CustomerAppClassifyPadRelation customerAppClassifyPadRelation : customerAppClassifyPadRelationList) {
                        List<Long> padCodeAppClassifyIds = padAppClassifyMap
                                .get(customerAppClassifyPadRelation.getPadCode());
                        if (padCodeAppClassifyIds == null) {
                            padCodeAppClassifyIds = new ArrayList<>();
                            padAppClassifyMap.put(customerAppClassifyPadRelation.getPadCode(), padCodeAppClassifyIds);
                        }
                        padCodeAppClassifyIds.add(customerAppClassifyPadRelation.getAppClassifyId());
                        appClassifyIds.add(customerAppClassifyPadRelation.getAppClassifyId());
                    }
                    // 用这些黑名单id查出所有关联应用
                    List<CustomerAppClassifyRelation> customerAppClassifyRelationList = customerAppClassifyRelationMapper
                            .selectList(new QueryWrapper<>(CustomerAppClassifyRelation.class)
                                    .in("app_classify_id", appClassifyIds));
                    if (CollUtil.isNotEmpty(customerAppClassifyRelationList)) {
                        padWhiteMap = new HashMap<>();
                        for (CustomerAppClassifyRelation customerAppClassifyRelation : customerAppClassifyRelationList) {
                            List<String> pckNames = padWhiteMap.get(customerAppClassifyRelation.getAppClassifyId());
                            if (pckNames == null) {
                                pckNames = new ArrayList<>();
                                padWhiteMap.put(customerAppClassifyRelation.getAppClassifyId(), pckNames);
                            }
                            pckNames.add(customerAppClassifyRelation.getPackageName());
                        }
                    }
                }
            } else {
                // 直接取该客户下所有白名单关联的应用包名
                List<CustomerAppClassify> customerAppClassifies = customerAppClassifyMapper
                        .selectList(new QueryWrapper<>(CustomerAppClassify.class)
                                .eq("customer_id", customerId)
                                .eq("classify_type", 1)
                                .eq("delete_flag", 0));
                if (CollUtil.isNotEmpty(customerAppClassifies)) {
                    List<Long> appClassifyIds = customerAppClassifies.stream().map(CustomerAppClassify::getId)
                            .collect(Collectors.toList());
                    List<CustomerAppClassifyRelation> customerAppClassifyRelationList = customerAppClassifyRelationMapper
                            .selectList(new QueryWrapper<>(CustomerAppClassifyRelation.class)
                                    .select("package_name")
                                    .in("app_classify_id", appClassifyIds)
                                    .eq("customer_id", customerId));
                    if (CollUtil.isNotEmpty(customerAppClassifyRelationList)) {
                        if (whitelists == null) {
                            whitelists = new ArrayList<>();
                        }
                        Set<String> pckNames = customerAppClassifyRelationList.stream()
                                .map(CustomerAppClassifyRelation::getPackageName).collect(Collectors.toSet());
                        whitelists.addAll(pckNames);
                    }
                }
            }
        }

        /*
         * if(CollUtil.isEmpty(whitelists) && CollUtil.isEmpty(padWhiteMap)){
         * throw new BasicException(NEW_WHITELIST_NOT_EXIST);
         * }
         */

        SourceTargetEnum sourceTarget = param.getSourceCode() == null ? PAAS : param.getSourceCode();

        PadCMDForwardDTO padCMDForwardDTO = new WhiteListCMDDTO().setWhitelists(whitelists).setPadWhiteMap(padWhiteMap)
                .setPadAppClassifyMap(padAppClassifyMap)
                .builderForwardDTO(padCodes, sourceTarget, String.valueOf(param.getCustomerId()));
        PadTaskBO padTaskBO = padTaskComponent.addPadCMDTask(customerId, padCodes, APP_WHITE_LIST,
                padCMDForwardDTO);
        return GeneratePadTaskVO.builder(padTaskBO);
    }

    @Override
    public List<String> existInstructionPadCode(ExistInstructionPadCodeDTO param) {
        InstructionPadCodeDTO instructionPadCodeDTO = new InstructionPadCodeDTO();
        instructionPadCodeDTO.setPadCodes(param.getPadCodes());
        instructionPadCodeDTO.setCustomerId(param.getCustomerId());
        instructionPadCodeDTO.setTaskType(param.getTaskType());
        return padTaskService.existInstructionPadCode(instructionPadCodeDTO);
    }

    @Override
    public GeneratePadTaskVO replacePadAndroidPropByCountry(ReplacePadAndroidPropDTO requestParam) {
        // 防止空初始化一下
        if (Objects.isNull(requestParam.getProps())) {
            requestParam.setProps(Maps.newHashMap());
        }
        // 默认国家码 @TODO 这个默认国家码需要后续从集群信息中获取
        // 目前集群信息中没有国家码,所以这里先写死
        final String countryCode;
        // 检测国家码
        if (null != requestParam.getCountryCode() && !requestParam.getCountryCode().isEmpty() &&
                requestParam.getCountryCode().length() == 2) {
            countryCode = requestParam.getCountryCode();
        } else {
            countryCode = "SG";
        }
        // 传入国家,生成一下对应的sim卡信息,然后合并到传入的安卓属性中
        if (StringUtils.isNotBlank(requestParam.getPadCode())) {
            JSONObject props = new JSONObject();

            AndroidDeviceInfoUtils.ramdomSimAndGPSInfo(countryCode, props);
            // AndroidDeviceInfoUtils.ramdomGPSInfo(countryCode, props);
            AndroidDeviceInfoUtils.ramdomBatteryInfo(countryCode, props);
            AndroidDeviceInfoUtils.ramdomWifiInfo(countryCode, props);
            AndroidDeviceInfoUtils.ramdomBluetoothInfo(countryCode, props);

            Map<String, String> map = props.toJavaObject(Map.class);
            // 用户有传安卓属性,优先使用用户的
            map.putAll(requestParam.getProps());
            requestParam.setProps(map);
        }
        // 既没有传国家code 也没有传安卓属性,直接抛出异常
        if (Optional.ofNullable(requestParam.getProps()).map(Map::isEmpty).orElse(true)) {
            throw new BasicException(PARAM_REQUEST_ILLEGALITY);
        }
        UpdatePadAndroidPropDTO dto = new UpdatePadAndroidPropDTO();
        dto.setPadCode(requestParam.getPadCode());
        dto.setCustomerId(requestParam.getCustomerId());
        dto.setProps(requestParam.getProps());
        // 必须重启设备
        dto.setRestart(true);
        return updatePadAndroidProp(dto);
    }

    private JSONObject getAndroidProp(String countryStr, String av) {
        JSONObject props = new JSONObject();
        AndroidDeviceInfo androidDeviceInfo = androidDeviceInfoService.findOneRandom(av);
        if (Objects.nonNull(androidDeviceInfo)) {
            String device = androidDeviceInfo.getDevice();
            String brand = androidDeviceInfo.getBrand();
            String build_type = androidDeviceInfo.getBuildType();
            String android_version = androidDeviceInfo.getAndroidVersion();
            String build_id = androidDeviceInfo.getBuildId();
            String build_tags = androidDeviceInfo.getTags();
            String build_version = androidDeviceInfo.getBuildVersion();
            String fingerprint = androidDeviceInfo.getFingerprint();
            String platform = androidDeviceInfo.getPlatform();
            props.put("ro.product.brand", brand);
            props.put("ro.product.model", androidDeviceInfo.getModel());
            props.put("ro.product.manufacturer", brand);
            props.put("ro.product.device", device);
            props.put("ro.product.name", androidDeviceInfo.getProduct());
            props.put("ro.build.version.incremental", build_version);
            props.put("ro.build.id", build_id);
            props.put("ro.build.display.id", build_id);
            props.put("ro.build.fingerprint", fingerprint);
            props.put("ro.build.type", build_type);
            props.put("ro.build.tags", build_tags);
            props.put("ro.board.platform", platform);

            props.put("ro.build.flavor", device + "-" + build_type);
            props.put("ro.product.board", device);
            props.put("ro.build.product", device);
            props.put("ro.hardware", device);

            props.put("ro.build.description", device + "-" + build_type + " " + android_version + " " + build_id + " "
                    + build_version + " " + build_tags);

            props.put("ro.odm.build.fingerprint", fingerprint);
            props.put("ro.product.build.fingerprint", fingerprint);
            props.put("ro.system.build.fingerprint", fingerprint);
            props.put("ro.system_ext.build.fingerprint", fingerprint);
            props.put("ro.vendor.build.fingerprint", fingerprint);

            props.put("ro.product.build.id", build_id);
            props.put("ro.system.build.id", build_id);
            props.put("ro.system_ext.build.id", build_id);
            props.put("ro.vendor.build.id", build_id);

            props.put("ro.product.odm.device", device);
            props.put("ro.product.odm.model", device);
            props.put("ro.product.odm.name", device);
            props.put("ro.product.product.device", device);
            props.put("ro.product.product.model", device);
            props.put("ro.product.product.name", device);
            props.put("ro.product.system.device", device);
            props.put("ro.product.system.model", device);
            props.put("ro.product.system.name", device);
            props.put("ro.product.system_ext.device", device);
            props.put("ro.product.system_ext.model", device);
            props.put("ro.product.system_ext.name", device);
            props.put("ro.product.vendor.device", device);
            props.put("ro.product.vendor.model", device);
            props.put("ro.product.vendor.name", device);
        }
        return props;
    }

    @Override
    public void verifyPadBackupAndRestoreStatus(List<String> padCodes) throws BasicException {
        List<PadStatus> padStatuses = padStatusMapper.listByPadCodes(padCodes);
        String backupPadCodesStr = padStatuses.stream()
                .filter(padStatus -> Objects.equals(padStatus.getPadStatus(), PadStatusConstant.BACKUP))
                .map(PadStatus::getPadCode)
                .collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(backupPadCodesStr)) {
            throw new BasicException("实例[" + backupPadCodesStr + "]处于备份中");
        }

        String restorePadCodesStr = padStatuses.stream()
                .filter(padStatus -> Objects.equals(padStatus.getPadStatus(), PadStatusConstant.RESTORE))
                .map(PadStatus::getPadCode)
                .collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(restorePadCodesStr)) {
            throw new BasicException("实例[" + backupPadCodesStr + "]处于恢复中");
        }
    }

    @Override
    public void verifyPadBackupAndRestoreStatusByIps(List<String> deviceIps) throws BasicException {
        List<PadStatus> pads = padStatusMapper.listByDeviceIps(deviceIps);
        List<String> padCodes = pads.stream().map(PadStatus::getPadCode).collect(Collectors.toList());
        verifyPadBackupAndRestoreStatus(padCodes);
    }

    @Override
    public List<PadEdgeClusterVO> listPadEdgeClusterInfo(PadCodesDTO padCodesDTO) {
        return padMapper.listPadEdgeClusterInfo(padCodesDTO.getPadCodes());
    }

    @Override
    public Page<PadDetailsVO> padDetailsService(PadDetailsDTO param) {
        PageHelper.startPage(param.getPage(), param.getRows());
        List<PadDetailsVO> padDetailsVOS = padMapper.selectDetailsByPadCode(param);
        padDetailsVOS.forEach(padDetailsVO -> {
            padDetailsVO.setDcInfo(dcInfoMapper.selectDcInfoByCode(padDetailsVO.getDcId()));
        });

        return new Page<PadDetailsVO>(padDetailsVOS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePadStreamStatusService(List<String> padCodes, Integer streamStatus) {
        Pad updatePad = new Pad();
        updatePad.setStreamStatus(streamStatus);
        updatePad.setUpdateBy("");
        updatePad.setUpdateTime(new Date());
        return padMapper.update(updatePad, new QueryWrapper<Pad>().in("pad_code", padCodes)) > ZERO;
    }

    @Override
    public Pad getPadInfoByCustomerId(PadCustomerDTO padCustomerDTO) {
        return padMapper.selectPadByPadCustomerDTO(padCustomerDTO);
    }

    @Override
    public Page<PadListVO> padListService(PadListDTO param) {

        if (CollUtil.isNotEmpty(param.getGroupIds())) {
            List<PadGroup> groupId = padGroupMapper
                    .selectList(new QueryWrapper<PadGroup>().in("group_id", param.getGroupIds()));
            if (CollectionUtils.isEmpty(groupId)) {
                throw new BasicException(GROUP_NOT_EXIST);
            }
        }
        PageHelper.startPage(param.getPage(), param.getRows());
        return new Page<>(padMapper.selectPadListVO(param));
    }

    @Override
    public Pad getByCloudVendorTypeAndPadOutCode(GetPadByCloudVendorDTO dto) {
        return padMapper.getByCloudVendorTypeAndPadOutCode(dto.getCloudVendorType(), dto.getPadOutCode());
    }

    @Override
    public PadInfoVO getPadInfoByCode(String padCode) {
        return padMapper.selectPadInfoByCode(padCode);
    }

    @Override
    public List<PadIdleListVO> padIdleListVOService() {
        List<PadIdleListVO> list = padMapper.padIdleListVO();
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<Pad> listAll() {
        return padMapper.listAll();
    }

    @Override
    public void updatePadAdbOpenStatus(List<String> padCode, Integer adbOpenStatus) {
        Pad updatePad = new Pad();
        updatePad.setAdbOpenStatus(adbOpenStatus);
        updatePad.setUpdateTime(new Date());
        padMapper.update(updatePad, new QueryWrapper<Pad>().in("pad_code", padCode));
    }

    @Override
    public Pad getPadByOutCodeAndIp(String padOutCode, String padIp) {
        String key = RedisKeyPrefix.PAD_OUT_CODE_KEY + padOutCode;
        Object padObject = redisService.getCacheObject(key);
        if (ObjectUtil.isNull(padObject)) {
            Pad pad = padMapper.selectOne(new QueryWrapper<Pad>().eq("pad_out_code", padOutCode).eq("pad_ip", padIp));
            String padJson = com.alibaba.fastjson.JSON.toJSONString(pad);
            redisService.setCacheObject(key, padJson, RedisKeyTime.minute_60, TimeUnit.MINUTES);
            return pad;
        }
        return JSON.parseObject(padObject.toString(), Pad.class);
    }

    @Override
    public List<PadModelInfoVO> PadModelInfo(PadModelInfoDTO dto) {
        List<PadProperties> list = padPropertiesMapper.selectPropertiesByCodes(dto.getPadCodes());

        return list.parallelStream().map(properties -> {
            PadModelInfoVO data = new PadModelInfoVO();
            data.setPadCode(properties.getPadCode());
            data.setRomVersion(properties.getRomVersion());
            // 遍历 propertiesValues 并为 data 设置属性
            properties.getPropertiesValues().forEach(propertiesSub -> {
                BiConsumer<PadModelInfoVO, String> setter = PROPERTY_SETTERS.get(propertiesSub.getPropertiesName());
                if (setter != null) {
                    setter.accept(data, propertiesSub.getPropertiesValue());
                }
            });
            return data;
        }).collect(Collectors.toList());
    }

    private static final Map<String, BiConsumer<PadModelInfoVO, String>> PROPERTY_SETTERS = new HashMap<>();

    static {
        PROPERTY_SETTERS.put("imei", (data, value) -> data.setImei(value));
        PROPERTY_SETTERS.put("prop.ro.serialno", (data, value) -> data.setSerialno(value));
        PROPERTY_SETTERS.put("wifiMac", (data, value) -> data.setWifimac(value));
        PROPERTY_SETTERS.put("android_id", (data, value) -> {
            if (StringUtils.isNotEmpty(value)) {
                data.setAndroidid(value.trim());
            }
        });
        PROPERTY_SETTERS.put("ro.product.model", (data, value) -> data.setModel(value));
        PROPERTY_SETTERS.put("ro.product.brand", (data, value) -> data.setBrand(value));
        PROPERTY_SETTERS.put("ro.product.manufacturer", (data, value) -> data.setManufacturer(value));
        PROPERTY_SETTERS.put("persist.sys.su.enabled", (data, value) -> data.setIsRoot(value));
        PROPERTY_SETTERS.put("ro.aic.displaywidth", (data, value) -> data.setWidth(Integer.parseInt(value)));
        PROPERTY_SETTERS.put("ro.aic.displayheight", (data, value) -> data.setHeight(Integer.parseInt(value)));
        PROPERTY_SETTERS.put("bluetoothaddr", (data, value) -> data.setBluetoothaddr(value));
        PROPERTY_SETTERS.put("phonenum", (data, value) -> data.setPhonenum(value));
        // 储存大小从pad上获取
        // PROPERTY_SETTERS.put("dataSize", (data, value) -> data.setDataSize(value));
        // PROPERTY_SETTERS.put("dataSizeAvailable", (data, value) ->
        // data.setDataSizeAvailable(value));
        // PROPERTY_SETTERS.put("dataSizeUsed", (data, value) ->
        // data.setDataSizeUsed(value));

    }

//    @Override
//    public List<ConsoleDcInfoVO> getDcIdGroupByPadCodes(List<String> padCodes) {
//        return padMapper.getDcIdGroupByPadCodes(padCodes);
//    }

    @Override
    public BlacklistVO getBlacklistService(AppBlacklistDTO param) {
        Pad padCodeInfo = padMapper.selectOne(new QueryWrapper<Pad>().eq("pad_code", param.getPadCode()));
        if (padCodeInfo == null) {
            return null;
        }
        Long customerId = padCodeInfo.getCustomerId();
        String deviceLevel = padCodeInfo.getDeviceLevel();

        BlacklistVO blacklistVO = new BlacklistVO();
        List<String> blacklists = appBlackMapper.selectBlackAppPkgList(customerId, deviceLevel);

        // blacklists.add("net.onething.phonepal");
        // blacklists.add("com.example.titan_app");

        // 同一个客户下，同一个模式不能同时存在，这里先判断该客户的黑白名单模式
        CustomerAppClassify customerAppClassify = customerAppClassifyMapper
                .selectOne(new QueryWrapper<>(CustomerAppClassify.class)
                        .eq("customer_id", padCodeInfo.getCustomerId())
                        .eq("classify_type", 2)
                        .eq("delete_flag", 0)
                        .last("limit 1"));
        // 获取到新黑白名单中黑名单列表
        if (customerAppClassify != null) {
            List<CustomerAppClassifyRelation> customerAppClassifyRelationList = new ArrayList<>();
            if (customerAppClassify.getApplyAllInstances()) {
                customerAppClassifyRelationList = customerAppClassifyRelationMapper
                        .selectList(new QueryWrapper<>(CustomerAppClassifyRelation.class)
                                .eq("app_classify_id", customerAppClassify.getId())
                                .eq("customer_id", customerId));
            } else {
                List<String> padCodes = Arrays.asList(param.getPadCode());
                List<CustomerAppClassifyPadRelation> customerAppClassifyPadRelationList = customerAppClassifyPadRelationMapper
                        .selectAppClassifyPadListByType(padCodeInfo.getCustomerId(), padCodes, 2);
                if (CollUtil.isNotEmpty(customerAppClassifyPadRelationList)) {
                    Set<Long> appClassifyIds = customerAppClassifyPadRelationList.stream()
                            .map(CustomerAppClassifyPadRelation::getAppClassifyId).collect(Collectors.toSet());
                    customerAppClassifyRelationList = customerAppClassifyRelationMapper
                            .selectList(new QueryWrapper<>(CustomerAppClassifyRelation.class)
                                    .in("app_classify_id", appClassifyIds));
                }
            }

            if (CollUtil.isNotEmpty(customerAppClassifyRelationList)) {
                Set<String> blacklistSet = new HashSet<>();
                if (blacklists == null) {
                    blacklists = new ArrayList<>();
                }
                for (CustomerAppClassifyRelation customerAppClassifyRelation : customerAppClassifyRelationList) {
                    blacklistSet.add(customerAppClassifyRelation.getPackageName());
                }
                blacklists.addAll(new ArrayList<>(blacklistSet));
            }
        }
        blacklistVO.setBlacklists(blacklists);
        return blacklistVO;
    }

    @Override
    public List<GeneratePadTaskVO> limitBandwidthService(LimitBandwidthDTO param) {
        long customerId = param.getCustomerId();
        List<String> padCodes = padMapper.getPadCodesByGroupIds(param.getPadCodes(), null, customerId);
        if (CollectionUtils.isEmpty(padCodes)) {
            throw new BasicException(PAD_CODE_NOT_EXIST);
        }

        SourceTargetEnum sourceTarget = Optional.of(param).map(LimitBandwidthDTO::getTaskSource)
                .orElse(SourceTargetEnum.PAAS);
        String oprBy = Optional.of(param).map(LimitBandwidthDTO::getOprBy).orElse(String.valueOf(customerId));
        Consumer<AddPadTaskDTO> padTaskConsumer = addPadTaskDTO -> {
            SpeedLimitVO speedLimitVO = new SpeedLimitVO(param.getUpBandwidth(), param.getDownBandwidth());
            addPadTaskDTO.setTaskContent(JSON.toJSONString(speedLimitVO));
            addPadTaskDTO.setCreateBy(oprBy);
        };

        PadNetworkSpeedLimitBO limitBO = new PadNetworkSpeedLimitBO();
        limitBO.setUpBandwidth(param.getUpBandwidth());
        limitBO.setDownBandwidth(param.getDownBandwidth());
        PadTaskBO padTaskBO = padTaskComponent.addPadTask(customerId, padCodes, LIMIT_BANDWIDTH, padTaskConsumer,
                JSON.toJSONString(limitBO), sourceTarget);
        return GeneratePadTaskVO.builder(padTaskBO);
    }

    /**
     * 由cms任务执行成功成功后，监听任务结果回调，更新pad的带宽信息
     *
     * @param param PadBandwidthDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePadBandwidthService(PadBandwidthDTO param) {
        Pad updatePad = new Pad();
        updatePad.setUpBandwidth(param.getUpBandwidth());
        updatePad.setDownBandwidth(param.getDownBandwidth());

        return padMapper.update(updatePad, new QueryWrapper<Pad>().eq("pad_code", param.getPadCode())) > 0;
    }

    @Override
    public List<GeneratePadTaskVO> updateTimeZone(UpdatePadTimeZoneDTO param) {
        PadCMDForwardDTO padCMDForwardDTO = new UpdatePadTimeZoneCMDDTO()
                .setTimeZone(param.getTimeZone())
                .builderForwardDTO(param.getPadCodes(), PAAS);
        PadTaskBO padTaskBO = padTaskComponent.addPadCMDTask(param.getCustomerId(), param.getPadCodes(),
                CHANGE_TIME_ZONE, padCMDForwardDTO);
        return GeneratePadTaskVO.builder(padTaskBO);
    }

    @Override
    public List<GeneratePadTaskVO> updateLanguage(UpdatePadLanguageDTO param) {
        PadCMDForwardDTO padCMDForwardDTO = new UpdatePadLanguageCMDDTO()
                .setLanguage(param.getLanguage())
                .setCountry(param.getCountry())
                .builderForwardDTO(param.getPadCodes(), PAAS);
        PadTaskBO padTaskBO = padTaskComponent.addPadCMDTask(param.getCustomerId(), param.getPadCodes(),
                CHANGE_LANGUAGE, padCMDForwardDTO);
        return GeneratePadTaskVO.builder(padTaskBO);
    }

    @Override
    public List<GeneratePadTaskVO> updateSIM(UpdatePadSIMDTO param) {
        UpdatePadSIMCMDDTO updatePadSIMCMDDTO = new UpdatePadSIMCMDDTO();
        BeanUtils.copyProperties(param, updatePadSIMCMDDTO);
        PadCMDForwardDTO padCMDForwardDTO = updatePadSIMCMDDTO.builderForwardDTO(param.getPadCodes(), PAAS);
        PadTaskBO padTaskBO = padTaskComponent.addPadCMDTask(param.getCustomerId(), param.getPadCodes(),
                UPDATE_SIM, padCMDForwardDTO);
        return GeneratePadTaskVO.builder(padTaskBO);
    }

    @Override
    public WhitelistVO getWhitelistService(AppBlacklistDTO param) {
        Pad padCodeInfo = padMapper.selectOne(new QueryWrapper<Pad>().eq("pad_code", param.getPadCode()));
        Long customerId = padCodeInfo.getCustomerId();
        String deviceLevel = padCodeInfo.getDeviceLevel();

        WhitelistVO whitelistVO = new WhitelistVO();
        List<String> blacklists = appWhiteMapper.selectWhiteAppPkgList(customerId, deviceLevel);

        // 同一个客户下，同一个模式不能同时存在，这里先判断该客户的黑白名单模式
        CustomerAppClassify customerAppClassify = customerAppClassifyMapper
                .selectOne(new QueryWrapper<>(CustomerAppClassify.class)
                        .eq("customer_id", padCodeInfo.getCustomerId())
                        .eq("classify_type", 1)
                        .eq("delete_flag", 0)
                        .last("limit 1"));
        // 获取到新黑白名单中白名单列表
        if (customerAppClassify != null) {
            List<CustomerAppClassifyRelation> customerAppClassifyRelationList = new ArrayList<>();
            if (customerAppClassify.getApplyAllInstances()) {
                customerAppClassifyRelationList = customerAppClassifyRelationMapper
                        .selectList(new QueryWrapper<>(CustomerAppClassifyRelation.class)
                                .eq("app_classify_id", customerAppClassify.getId())
                                .eq("customer_id", customerId));
            } else {
                List<String> padCodes = Arrays.asList(param.getPadCode());
                List<CustomerAppClassifyPadRelation> customerAppClassifyPadRelationList = customerAppClassifyPadRelationMapper
                        .selectAppClassifyPadListByType(padCodeInfo.getCustomerId(), padCodes, 1);
                if (CollUtil.isNotEmpty(customerAppClassifyPadRelationList)) {
                    Set<Long> appClassifyIds = customerAppClassifyPadRelationList.stream()
                            .map(CustomerAppClassifyPadRelation::getAppClassifyId).collect(Collectors.toSet());
                    customerAppClassifyRelationList = customerAppClassifyRelationMapper
                            .selectList(new QueryWrapper<>(CustomerAppClassifyRelation.class)
                                    .in("app_classify_id", appClassifyIds));
                }
            }

            if (CollUtil.isNotEmpty(customerAppClassifyRelationList)) {
                Set<String> blacklistSet = new HashSet<>();
                if (blacklists == null) {
                    blacklists = new ArrayList<>();
                }
                for (CustomerAppClassifyRelation customerAppClassifyRelation : customerAppClassifyRelationList) {
                    blacklistSet.add(customerAppClassifyRelation.getPackageName());
                }
                blacklists.addAll(new ArrayList<>(blacklistSet));
            }
        }
        whitelistVO.setWhitelists(blacklists);
        return whitelistVO;
    }

    /**
     * 校验安卓版本
     *
     * @param padCodes
     * @param newImageId             升级到的镜像id
     * @param newRealPhoneTemplateId 升级到的真机模板id 可为空
     */
    private void checkAndroidVersion(List<String> padCodes, String newImageId, Long newRealPhoneTemplateId,Boolean wipeData) {

        // 根据pad的imageId获取安卓版本
        Integer androidVersion = customerUploadImageMapper.getAndroidVersionByImageUniqueId(newImageId);
        if (null == androidVersion) {
            throw new BasicException("[" + newImageId + "] 镜像数据异常，无法升级");
        }
        String newImageRomVersion = androidVersion + "";
        // 查询当前真机模板的安卓版本
//        if (newRealPhoneTemplateId != null && BooleanUtil.isFalse(wipeData)) {
//           throw new BasicException("跨系统升级镜像或更换真机adi模板需要清除用户数据。");
//        }
        // 查询当前真机模板的安卓版本
        String newRealPhoneTemplateRomVersion = null;
        if (newRealPhoneTemplateId != null) {
            RealPhoneTemplate realPhoneTemplate = realPhoneTemplateMapper.selectById(newRealPhoneTemplateId);
            newRealPhoneTemplateRomVersion = realPhoneTemplate.getAndroidImageVersion() + "";
            // 如果目标真机模板和镜像的安卓版本一致，就不需要care当前pad的系统版本了, 直接返回即可

        }

        // 批量查询padCode的类型及其镜像对应的安卓版本
        List<Pad> padList = padMapper.selectList(new QueryWrapper<>(Pad.class)
                .select("pad_code", "image_id", "type").in("pad_code", padCodes));
        if (CollUtil.isEmpty(padList)) {
            throw new BasicException("[" + String.join(",", padCodes) + "] 实例数据异常，无法升级");

        }


        // 比较 云真机类型下 padCode安卓版本和升级的镜像安卓版本和真机模板安卓版本一致
        // 否则提示
        // padCode当前安卓版本不支持升级此镜像；padCode当前安卓版本不支持升级此真机模板；padCode当前安卓版本不支持升级此镜像和此真机模板
        Set<String> failPadCodes = new HashSet<>();
        boolean failImage = false;
        boolean failReal = false;
        for (Pad pad : padList) {
            //跨安卓版本选择清除数据都可以升级镜像
//            if (PadConstants.Type.REAL.getValue().equals(pad.getType())) {
            Integer oldAndroidVersion = customerUploadImageMapper.getAndroidVersionByImageUniqueId(pad.getImageId());
            if (null == oldAndroidVersion) {
                throw new BasicException("[" + pad.getImageId() + "] 镜像数据异常，无法升级");
            }
            String padRomVersion = oldAndroidVersion + "";
            if (!padRomVersion.equals(newImageRomVersion)) {
                failPadCodes.add(pad.getPadCode());
                failImage = true;
            }
            if (StrUtil.isNotEmpty(newRealPhoneTemplateRomVersion)
                    //这里目前允许跨安卓版本升级。此方法只有升级镜像调用,所以这里验证模板安卓版本应该跟新值比较
                    && !newImageRomVersion.equals(newRealPhoneTemplateRomVersion)) {
                failPadCodes.add(pad.getPadCode());
                failReal = true;
            }
//            }
        }
        String errMsg = String.join(",", failPadCodes);
        if (CollUtil.isNotEmpty(failPadCodes) && BooleanUtil.isFalse(wipeData)) {
            if (failImage) {
                errMsg += "当前安卓版本不支持升级此镜像";
            }
            throw new BasicException(errMsg);
        }
        if(failReal){
            errMsg += "当前安卓版本不支持升级此镜像和此真机模板";
            throw new BasicException(errMsg);
        }
    }

    /**
     * 生成实例编号
     * 实例编号新规则 总共16位
     * 规则：<厂商><实例><日期><7位随机数字或者大写字母>
     * 例如：<AC>
     * <p>
     * <YYMMDD><87F3A9C>
     *
     * @param deviceCode
     * @return
     */
    private String generatePadCode(String deviceCode) {
        String padCode = deviceCode.substring(ZERO, TWO);
        padCode += "P";
        String nowDate = DateUtil.format(new Date(), "yyMMdd");
        padCode = padCode + nowDate;
        return (padCode + PadCodeRamdomUtil.generateRandomString(7)).toUpperCase();
    }

    /**
     * 更新通讯录
     */
    @Override
    public List<GeneratePadTaskVO> updateContacts(UpdateContactsDTO param) {
        String fileUniqueId = param.getFileUniqueId();
        Object info = param.getInfo();
        if (StringUtils.isBlank(fileUniqueId) && info == null) {
            throw new BasicException("fileUniqueId and info cannot null");
        }

        CustomerFileVO customerFileVO = null;
        long customerId = param.getCustomerId();
        if (StringUtils.isNotBlank(fileUniqueId)) {
            customerFileVO = fileManager.getCustomerExistingFile(customerId, fileUniqueId);
        }
        PadCMDForwardDTO padCMDForwardDTO = new PadContactsCMDDTO()
                .setInfo(info)
                .setImportFile(fileUniqueId)
                .builderForwardDTO(param.getPadCodes(), PAAS);

        // 根据pad拼接不同文件下载地址
        CustomerFileVO finalCustomerFileVO = customerFileVO;
        padCMDForwardDTO.getPadInfos().forEach(padInfoDTO -> {
            String padCode = padInfoDTO.getPadCode();

            if (finalCustomerFileVO != null) {
                String fileDownloadUrl = finalCustomerFileVO.getPublicUrl();
                PadContactsCMDDTO data = (PadContactsCMDDTO) padInfoDTO.getData();
                PadContactsCMDDTO newData = JSON.parseObject(JSON.toJSONString(data), PadContactsCMDDTO.class);
                newData.setImportFile(fileDownloadUrl);
                padInfoDTO.setData(newData);
            }

        });

        return GeneratePadTaskVO.builder(padTaskComponent.addPadCMDTask(customerId, param.getPadCodes(), UPDATE_CONTACTS, padCMDForwardDTO));
    }

    @Override
    public void updatePadDns(String padCode, String dns) {
        padMapper.updatePadDns(padCode, dns);
    }


    @Override
    public List<GeneratePadTaskVO> injectAudioToMic(AudioToMicDTO param) {
        String url = resolveAudioUrl(param);
        InjectAudioToMicCMDDTO audioCmdDto = new InjectAudioToMicCMDDTO(url,param.getEnable());

        PadCMDForwardDTO padCmdForwardDto = audioCmdDto.builderForwardDTO(param.getPadCodes(), PAAS);
        PadTaskBO padTaskBo = padTaskComponent.addPadCMDTask(
                param.getCustomerId(),
                param.getPadCodes(),
                INJECTION_AUDIO,
                padCmdForwardDto
        );

        return GeneratePadTaskVO.builder(padTaskBo);
    }

    private String resolveAudioUrl(AudioToMicDTO param) {
        boolean isUrlBlank = StringUtils.isBlank(param.getUrl());
        boolean isFileUniqueIdBlank = StringUtils.isBlank(param.getFileUniqueId());

        if (isUrlBlank && isFileUniqueIdBlank) {
            throw new BasicException(AUDIOTOMIC_FILE_UNIQUEID_URL_NULL);
        }

        if (!isFileUniqueIdBlank) {
            CustomerFileVO customerFile = fileManager.getCustomerExistingFile(param.getCustomerId(), param.getFileUniqueId());

            if (customerFile != null && StringUtils.isNotBlank(customerFile.getPublicUrl())) {
                return customerFile.getPublicUrl();
            }
        }

        if (isUrlBlank) {
            throw new BasicException(AUDIOTOMIC_FILE_UNIQUEID_NULL);
        }

        return param.getUrl();
    }

    /**
     * 检查实例是否有执行中的同步备份任务
     *
     * @param padCode 实例编号
     * @return 是否有执行中的任务
     */
    private boolean hasExecutingBackupTask(String padCode) {
        // 通过关联查询确认是否有NET_SYNC_BACKUP类型的任务
        List<HasPadTaskVo> execTasks = padTaskMapper.checkTasksByTypeAndPadCode(
                TaskTypeConstants.NET_SYNC_BACKUP.getType(),
                padCode,
                Collections.singletonList(EXECUTING.getStatus())
        );

        return !execTasks.isEmpty();
    }

    /**
     * 取消实例的待执行同步备份任务
     *
     * @param padCode 实例编号
     */
    private void cancelPendingBackupTasks(String padCode) {
        try{
            // 查询待执行的同步备份任务
            List<HasPadTaskVo> pendingTasks = padTaskMapper.checkTasksByTypeAndPadCode(
                    TaskTypeConstants.NET_SYNC_BACKUP.getType(),
                    padCode,
                    Collections.singletonList(TaskStatusConstants.WAIT_EXECUTE.getStatus())
            );

            // 取消所有待执行任务
            for (HasPadTaskVo task : pendingTasks) {
                padTaskMapper.updateStatusAndTimeById(task.getPadTaskId(), TaskStatusConstants.CANCEL.getStatus(), new Date());
                // 同时取消redis中的任务
                redisTemplate.opsForZSet().remove(RedisTaskQueueConstants.NET_SYNC_FIFO_TASKID_QUEUE, task.getPadTaskId());
                redisTemplate.opsForHash().delete(RedisTaskQueueConstants.NET_SYNC_FIFO_TASK_HASH, task.getPadTaskId());
                log.info("Canceled pending backup task for padCode: {}, taskId: {}", padCode, task.getPadTaskId());
            }

            // 同时取消重试任务
            int canceledRetries = taskRetryService.cancelPendingRetryByPadCodeAndTaskType(padCode, TaskTypeConstants.NET_SYNC_BACKUP.getType());
            if (canceledRetries > 0) {
                log.info("Canceled {} pending retry tasks for padCode: {}", canceledRetries, padCode);
            }
        }catch (Exception e){
            log.error("cancelPendingBackupTasks error",e);
        }

    }

    @Override
    public void checkIp(String padCode,String useIp) {
        try {
            LambdaQueryWrapper<Pad> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Pad::getPadCode, padCode);
            Pad pad = padMapper.selectOne(wrapper);
            if (pad != null) {
                Integer status = pad.getStatus();
                if (!Objects.equals(status, ONE)) {

                    String padIp = pad.getPadIp();

                    StringBuilder sb = new StringBuilder();
                    sb.append("健康上报实例padCode:"+padCode+"当前使用IP:"+useIp);
                    sb.append("\n");
                    sb.append("该实例在数据库状态为:"+status);
                    sb.append("\n");

                    if(StrUtil.isNotBlank(padIp)){
                        LambdaQueryWrapper<Pad> wrapper2 = new LambdaQueryWrapper<>();
                        wrapper.eq(Pad::getPadIp, padIp);
                        wrapper.eq(Pad::getStatus, ONE);

                        List<Pad> pads = padMapper.selectList(wrapper2);
                        if(CollUtil.isNotEmpty(pads)){
                            List<String> collect = pads.stream().map(Pad::getPadCode).collect(Collectors.toList());
                            sb.append("该正在使用的IP,在服务端实际应用列表:"+JSON.toJSONString(collect));
                        }
                    }

                    DingTalkRobotClient.sendMessage(NET_WOER_STOTAGE_DING,springProfilesActive,sb.toString());
                }else{
                    String padIp = pad.getPadIp();
                    if (StrUtil.isBlank(padIp)) {
                        StringBuilder sb = new StringBuilder();
                        sb.append("健康上报实例padCode:"+padCode+"当前使用IP:"+useIp);
                        sb.append("\n");
                        sb.append("该实例在数据库状态为:"+status);
                        sb.append("\n");
                        sb.append("padIp:"+padIp);
                        sb.append("\n");
                        sb.append("逻辑上应为已销毁容器");
                        sb.append("\n");

                        String key = PAD_REPORTED_DING_TALK + padCode;
                        Boolean b = redisService.hasKey(key);
                        if (!b) {
                            DingTalkRobotClient.sendMessage(NET_WOER_STOTAGE_DING,springProfilesActive,sb.toString());
                            redisService.setCacheObject(key, "1", 5L, TimeUnit.MINUTES);
                        }


                    }
                }



            }
        }catch (Exception e){
            log.error("checkIp error",e);
        }
    }


    public PadServiceImpl(PadGroupMapper padGroupMapper, PadCommandManager padCommandManager,
                          PadOutMapper padOutMapper,
                          IPadStatusService padStatusService,
                          IPadPropertiesKeyService padPropertiesKeyService, PadMapper padMapper,
                          DcInfoMapper dcInfoMapper, PadPropertiesMapper padPropertiesMapper, PadManager padManager,
                          DcInfoManager dcInfoManager, DictManager dictManager, FileManager fileManager,
                          CustomerUploadImageMapper customerUploadImageMapper, PadStatusMapper padStatusMapper,
                          CommonPadTaskComponent commonPadTaskComponent, PadRoomMapper padRoomMapper, DevicePadMapper devicePadMapper,
                          PadOperLogMapper padOperLogMapper, AppBlackMapper appBlackMapper, RedisService redisService,
                          CommonPadCommandComponent commonPadCommandComponent,
                          AndroidDeviceInfoService androidDeviceInfoService,
                          IArmService armServerService,
                          PadInstalledAppInformationMapper padInstalledAppInformationMapper, PadMacLogMapper padMacLogMapper,
                          ScreenLayoutMapper screenLayoutMapper,
                          RealPhoneTemplateMapper realPhoneTemplateMapper,
                          CustomerAppClassifyPadRelationMapper customerAppClassifyPadRelationMapper,
                          CustomerAppClassifyRelationMapper customerAppClassifyRelationMapper,
                          NetStorageResPadMapper netStorageResPadMapper,
                          TaskInternalController taskInternalController,
                          AppWhiteMapper appWhiteMapper, AdiCertificateManager adiCertificateManager,
                          CustomerAppClassifyMapper customerAppClassifyMapper, RedissonDistributedLock redissonDistributedLock,
                          INetStorageResOffLogService netStorageResOffLogService,
                          NetStoragePadUnitDetailMapper netStoragePadUnitDetailMapper,
                          TaskRelInstanceDetailImageSuccMapper taskRelInstanceDetailImageSuccMapper,
                          ITaskService taskService, IPadTaskService padTaskService, ConfigurationMapper configurationMapper,
                          IEdgeClusterConfigurationService edgeClusterConfigurationService) {
        this.padGroupMapper = padGroupMapper;
        this.padCommandManager = padCommandManager;
        this.padOutMapper = padOutMapper;
        this.padStatusService = padStatusService;
        this.padPropertiesKeyService = padPropertiesKeyService;
        this.padMapper = padMapper;
        this.dcInfoMapper = dcInfoMapper;
        this.padPropertiesMapper = padPropertiesMapper;
        this.padManager = padManager;
        this.dcInfoManager = dcInfoManager;
        this.dictManager = dictManager;
        this.fileManager = fileManager;
        this.padTaskComponent = commonPadTaskComponent;
        this.customerUploadImageMapper = customerUploadImageMapper;
        this.padStatusMapper = padStatusMapper;
        this.padRoomMapper = padRoomMapper;
        this.devicePadMapper = devicePadMapper;
        this.padOperLogMapper = padOperLogMapper;
        this.appBlackMapper = appBlackMapper;
        this.redisService = redisService;
        this.padCommandComponent = commonPadCommandComponent;
        this.padMacLogMapper = padMacLogMapper;
        this.padInstalledAppInformationMapper = padInstalledAppInformationMapper;
        this.screenLayoutMapper = screenLayoutMapper;
        this.realPhoneTemplateMapper = realPhoneTemplateMapper;
        this.customerAppClassifyPadRelationMapper = customerAppClassifyPadRelationMapper;
        this.customerAppClassifyRelationMapper = customerAppClassifyRelationMapper;
        this.appWhiteMapper = appWhiteMapper;
        this.customerAppClassifyMapper = customerAppClassifyMapper;
        this.adiCertificateManager = adiCertificateManager;
        this.androidDeviceInfoService = androidDeviceInfoService;
        this.redissonDistributedLock = redissonDistributedLock;
        this.armServerService = armServerService;
        this.taskService = taskService;
        this.padTaskService = padTaskService;
        this.configurationMapper = configurationMapper;
        this.taskInternalController = taskInternalController;
        this.netStorageResOffLogService = netStorageResOffLogService;
        this.netStorageResPadMapper = netStorageResPadMapper;
        this.netStoragePadUnitDetailMapper = netStoragePadUnitDetailMapper;
        this.edgeClusterConfigurationService = edgeClusterConfigurationService;
        this.taskRelInstanceDetailImageSuccMapper = taskRelInstanceDetailImageSuccMapper;

    }
}
